<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库系统 - 答题</title>
    <link rel="stylesheet" href="/css/common.css">
    <style>
        .quiz-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 0 20px;
        }

        .quiz-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .quiz-header h1 {
            margin: 0 0 1rem 0;
            font-size: 2rem;
        }

        .quiz-info {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .quiz-info-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }

        .quiz-info-item .label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .quiz-info-item .value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 0.5rem;
        }

        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .question-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .question-number {
            color: #667eea;
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .question-type {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            display: inline-block;
        }

        .question-content {
            padding: 2rem;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            color: #333;
        }

        .question-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            margin: 1rem 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .options-container {
            margin: 2rem 0;
        }

        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .option-item:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .option-item.selected {
            border-color: #667eea;
            background: #e3f2fd;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }

        .option-label {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .option-content {
            flex: 1;
        }

        .option-text {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .option-image {
            max-width: 200px;
            height: auto;
            border-radius: 5px;
            margin-top: 0.5rem;
        }

        .explanation-section {
            margin-top: 2rem;
            border-top: 2px solid #e9ecef;
            padding-top: 1.5rem;
        }

        .explanation-toggle {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .explanation-toggle:hover {
            background: #e9ecef;
        }

        .explanation-toggle .toggle-icon {
            font-size: 1.2rem;
            transition: transform 0.3s;
        }

        .explanation-toggle.expanded .toggle-icon {
            transform: rotate(180deg);
        }

        .explanation-content {
            margin-top: 1rem;
            padding: 1.5rem;
            background: #f0f4ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
        }

        .explanation-content.show {
            display: block;
        }

        .explanation-text {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .explanation-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin-top: 1rem;
        }

        .answer-feedback {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 8px;
            display: none;
        }

        .answer-feedback.correct {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .answer-feedback.incorrect {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .answer-feedback.show {
            display: block;
        }

        .correct-answer-display {
            margin-top: 0.5rem;
            font-weight: bold;
        }

        /* 答题卡样式 */
        .answer-card {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 280px;
            max-height: 80vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
        }

        .answer-card-header {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .answer-card-title {
            margin: 0;
            font-size: 1.1rem;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .answer-card-body {
            padding: 1rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .answer-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin-bottom: 1rem;
        }

        .answer-item {
            width: 40px;
            height: 40px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            background: white;
        }

        .answer-item:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .answer-item.current {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .answer-item.answered {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }

        .answer-item.unanswered {
            border-color: #dc3545;
            background: #dc3545;
            color: white;
        }

        .answer-card-stats {
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
        }

        .stats-row:last-child {
            margin-bottom: 0;
        }

        .answer-card-toggle {
            position: fixed;
            right: 20px;
            top: 20px;
            width: 50px;
            height: 50px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            transition: all 0.3s;
        }

        .answer-card-toggle:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .close-card {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-card:hover {
            color: #333;
        }

        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2rem 0;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .question-progress {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            flex: 1;
            min-width: 200px;
        }

        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            transition: width 0.3s;
        }

        .timer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .quiz-container {
                padding: 0 15px;
            }

            .quiz-header {
                padding: 1.5rem;
            }

            .quiz-header h1 {
                font-size: 1.5rem;
            }

            .quiz-info {
                flex-direction: column;
            }

            .question-content {
                padding: 1.5rem;
            }

            .option-item {
                padding: 1rem;
            }

            .quiz-navigation {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <a href="/" class="logo">📚 题库系统</a>
            <ul class="nav-links">
                <li><a href="/">首页</a></li>
                <li><a href="/admin">管理后台</a></li>
            </ul>
        </div>
    </nav>

    <!-- 答题容器 -->
    <div class="quiz-container">
        <!-- 题库信息头部 -->
        <div id="quizHeader" class="quiz-header" style="display: none;">
            <h1 id="bankName">题库名称</h1>
            <div class="quiz-info">
                <div class="quiz-info-item">
                    <div class="label">总题数</div>
                    <div class="value" id="totalQuestions">0</div>
                </div>
                <div class="quiz-info-item">
                    <div class="label">当前题目</div>
                    <div class="value" id="currentQuestionNumber">1</div>
                </div>
                <div class="quiz-info-item">
                    <div class="label">已答题数</div>
                    <div class="value" id="answeredCount">0</div>
                </div>
            </div>
        </div>

        <!-- 题目卡片 -->
        <div id="questionCard" class="question-card" style="display: none;">
            <div class="question-header">
                <div class="question-number" id="questionNumber">第 1 题</div>
                <span class="question-type" id="questionType">单选题</span>
            </div>
            <div class="question-content">
                <div class="question-text" id="questionText"></div>
                <img id="questionImage" class="question-image" style="display: none;">

                <div class="options-container" id="optionsContainer">
                    <!-- 选项将通过JavaScript动态生成 -->
                </div>

                <!-- 答案反馈区域（即时反馈模式） -->
                <div id="answerFeedback" class="answer-feedback">
                    <div id="feedbackMessage"></div>
                    <div id="correctAnswerDisplay" class="correct-answer-display"></div>
                </div>

                <!-- 解析区域 -->
                <div id="explanationSection" class="explanation-section" style="display: none;">
                    <div class="explanation-toggle" id="explanationToggle" onclick="toggleExplanation()">
                        <span>📖 查看解析</span>
                        <span class="toggle-icon">▼</span>
                    </div>
                    <div class="explanation-content" id="explanationContent">
                        <div id="explanationText" class="explanation-text"></div>
                        <img id="explanationImage" class="explanation-image" style="display: none;">
                    </div>
                </div>
            </div>
        </div>

        <!-- 答题导航 -->
        <div id="quizNavigation" class="quiz-navigation" style="display: none;">
            <button id="prevBtn" class="btn btn-secondary" onclick="previousQuestion()">上一题</button>

            <div class="question-progress">
                <div>答题进度</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                </div>
                <div id="progressText">0 / 0</div>
            </div>

            <div class="timer" id="timer">
                <div>答题用时</div>
                <div id="timerDisplay">00:00:00</div>
            </div>

            <button id="nextBtn" class="btn btn-primary" onclick="nextQuestion()">下一题</button>
        </div>

        <!-- 提交按钮 -->
        <div id="submitContainer" style="text-align: center; margin: 2rem 0; display: none;">
            <button class="btn btn-success btn-lg" onclick="submitQuiz()" id="submitButton">提交答案</button>
        </div>
    </div>

    <!-- 答题卡切换按钮 -->
    <button class="answer-card-toggle" onclick="toggleAnswerCard()" title="答题卡">
        📋
    </button>

    <!-- 答题卡 -->
    <div class="answer-card" id="answerCard">
        <div class="answer-card-header">
            <h3 class="answer-card-title">
                答题卡
                <button class="close-card" onclick="toggleAnswerCard()">×</button>
            </h3>
        </div>
        <div class="answer-card-body">
            <div class="answer-card-stats">
                <div class="stats-row">
                    <span>总题数：</span>
                    <span id="cardTotalQuestions">0</span>
                </div>
                <div class="stats-row">
                    <span>已答题：</span>
                    <span id="cardAnsweredCount">0</span>
                </div>
                <div class="stats-row">
                    <span>未答题：</span>
                    <span id="cardUnansweredCount">0</span>
                </div>
            </div>
            <div class="answer-grid" id="answerGrid">
                <!-- 答题卡网格将通过JavaScript生成 -->
            </div>
            <div style="text-align: center; margin-top: 1rem;">
                <button class="btn btn-success" onclick="submitQuiz()" id="cardSubmitButton" style="display: none;">
                    完成答题
                </button>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>正在加载题目...</p>
        </div>
    </div>

    <script src="/js/common.js"></script>
    <script>
        // 全局变量
        let quizData = null;
        let currentQuestionIndex = 0;
        let userAnswers = {};
        let startTime = null;
        let timerInterval = null;
        let bankId = null;
        let shuffleQuestions = false;
        let shuffleOptions = false;
        let quizMode = 'exam'; // 'immediate' 或 'exam'

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            initializeQuiz();
        });

        // 初始化答题
        async function initializeQuiz() {
            try {
                // 从URL获取参数
                const pathParts = window.location.pathname.split('/');
                bankId = pathParts[pathParts.length - 1];

                const urlParams = new URLSearchParams(window.location.search);
                shuffleQuestions = urlParams.get('shuffle_questions') === 'true';
                shuffleOptions = urlParams.get('shuffle_options') === 'true';
                quizMode = urlParams.get('quiz_mode') || 'exam';

                if (!bankId || bankId === 'quiz') {
                    showMessage('无效的题库ID', 'error');
                    setTimeout(() => window.location.href = '/', 2000);
                    return;
                }

                // 加载题目数据
                await loadQuizData();

                // 隐藏加载遮罩
                document.getElementById('loadingOverlay').style.display = 'none';

                // 显示答题界面
                showQuizInterface();

                // 开始计时
                startTimer();

                // 显示第一题
                if (quizData.questions && quizData.questions.length > 0) {
                    showQuestion(0);
                }

            } catch (error) {
                console.error('初始化答题失败:', error);
                showMessage('加载题目失败: ' + error.message, 'error');
                setTimeout(() => window.location.href = '/', 3000);
            }
        }

        // 加载题目数据
        async function loadQuizData() {
            const url = `/answers/quiz/${bankId}?shuffle_questions=${shuffleQuestions}&shuffle_options=${shuffleOptions}&quiz_mode=${quizMode}`;
            const response = await apiGet(url);

            if (!response.success) {
                throw new Error(response.message || '加载题目失败');
            }

            quizData = response.data;

            if (!quizData.questions || quizData.questions.length === 0) {
                throw new Error('该题库暂无题目');
            }
        }

        // 显示答题界面
        function showQuizInterface() {
            // 显示题库信息
            document.getElementById('bankName').textContent = quizData.bank.name;
            document.getElementById('totalQuestions').textContent = quizData.questions.length;
            document.getElementById('quizHeader').style.display = 'block';

            // 显示题目卡片和导航
            document.getElementById('questionCard').style.display = 'block';
            document.getElementById('quizNavigation').style.display = 'flex';

            // 设置提交按钮文本
            const submitButton = document.getElementById('submitButton');
            const cardSubmitButton = document.getElementById('cardSubmitButton');
            if (quizMode === 'immediate') {
                submitButton.textContent = '结束答题';
                cardSubmitButton.textContent = '结束答题';
            } else {
                submitButton.textContent = '提交答案';
                cardSubmitButton.textContent = '完成答题';
            }

            // 初始化用户答案对象
            quizData.questions.forEach(question => {
                userAnswers[question.id] = [];
            });

            // 延迟初始化答题卡，确保数据已加载
            setTimeout(() => {
                initializeAnswerCard();
            }, 100);
        }

        // 开始计时
        function startTimer() {
            startTime = new Date();
            timerInterval = setInterval(updateTimer, 1000);
        }

        // 更新计时器显示
        function updateTimer() {
            if (!startTime) return;

            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);

            const hours = Math.floor(elapsed / 3600);
            const minutes = Math.floor((elapsed % 3600) / 60);
            const seconds = elapsed % 60;

            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timerDisplay').textContent = timeString;
        }

        // 显示指定题目
        function showQuestion(index) {
            if (index < 0 || index >= quizData.questions.length) return;

            currentQuestionIndex = index;
            const question = quizData.questions[index];

            // 更新题目信息
            document.getElementById('questionNumber').textContent = `第 ${index + 1} 题`;
            document.getElementById('questionType').textContent = question.question_type === 'single' ? '单选题' : '多选题';
            document.getElementById('currentQuestionNumber').textContent = index + 1;

            // 显示题目内容
            const questionTextElement = document.getElementById('questionText');
            const questionImageElement = document.getElementById('questionImage');

            if (question.question_text) {
                questionTextElement.textContent = question.question_text;
                questionTextElement.style.display = 'block';
            } else {
                questionTextElement.style.display = 'none';
            }

            if (question.question_image) {
                questionImageElement.src = question.question_image;
                questionImageElement.style.display = 'block';
            } else {
                questionImageElement.style.display = 'none';
            }

            // 显示选项
            renderOptions(question);

            // 显示解析（如果有）
            showExplanation(question);

            // 隐藏答案反馈
            hideAnswerFeedback();

            // 更新导航按钮状态
            updateNavigationButtons();

            // 更新进度
            updateProgress();

            // 更新答题卡
            updateAnswerCard();
        }

        // 渲染选项
        function renderOptions(question) {
            const container = document.getElementById('optionsContainer');
            const userAnswer = userAnswers[question.id] || [];
            const isMultiple = question.question_type === 'multiple';

            container.innerHTML = question.options.map(option => `
                <div class="option-item ${userAnswer.includes(option.option_label) ? 'selected' : ''}"
                     onclick="selectOption('${option.option_label}', ${isMultiple})">
                    <div class="option-label">${option.option_label}</div>
                    <div class="option-content">
                        ${option.option_text ? `<div class="option-text">${option.option_text}</div>` : ''}
                        ${option.option_image ? `<img src="${option.option_image}" class="option-image" alt="选项图片">` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 选择选项
        function selectOption(optionLabel, isMultiple) {
            const questionId = quizData.questions[currentQuestionIndex].id;
            const question = quizData.questions[currentQuestionIndex];

            if (isMultiple) {
                // 多选题
                const currentAnswers = userAnswers[questionId] || [];
                const index = currentAnswers.indexOf(optionLabel);

                if (index > -1) {
                    // 取消选择
                    currentAnswers.splice(index, 1);
                } else {
                    // 添加选择
                    currentAnswers.push(optionLabel);
                }

                userAnswers[questionId] = currentAnswers;
            } else {
                // 单选题
                userAnswers[questionId] = [optionLabel];
            }

            // 重新渲染选项以更新选中状态
            renderOptions(question);

            // 即时反馈模式：显示答案反馈
            if (quizMode === 'immediate') {
                showAnswerFeedback(question);
            }

            // 更新已答题数
            updateAnsweredCount();
        }

        // 更新已答题数
        function updateAnsweredCount() {
            const answeredCount = Object.values(userAnswers).filter(answer => answer.length > 0).length;
            document.getElementById('answeredCount').textContent = answeredCount;

            // 根据答题模式决定何时显示提交按钮
            if (quizMode === 'immediate') {
                // 即时反馈模式：任何时候都可以结束答题
                document.getElementById('submitContainer').style.display = 'block';
                document.getElementById('cardSubmitButton').style.display = 'block';
            } else {
                // 考试模式：所有题目都答完时显示提交按钮
                if (answeredCount === quizData.questions.length) {
                    document.getElementById('submitContainer').style.display = 'block';
                    document.getElementById('cardSubmitButton').style.display = 'block';
                } else {
                    document.getElementById('submitContainer').style.display = 'none';
                    document.getElementById('cardSubmitButton').style.display = 'none';
                }
            }

            // 更新答题卡
            updateAnswerCard();
        }

        // 更新进度
        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / quizData.questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `${currentQuestionIndex + 1} / ${quizData.questions.length}`;
        }

        // 更新导航按钮状态
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.disabled = currentQuestionIndex === 0;
            nextBtn.disabled = currentQuestionIndex === quizData.questions.length - 1;

            if (currentQuestionIndex === quizData.questions.length - 1) {
                nextBtn.textContent = '完成答题';
                nextBtn.onclick = () => document.getElementById('submitContainer').scrollIntoView();
            } else {
                nextBtn.textContent = '下一题';
                nextBtn.onclick = nextQuestion;
            }
        }

        // 上一题
        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                showQuestion(currentQuestionIndex - 1);
            }
        }

        // 下一题
        function nextQuestion() {
            if (currentQuestionIndex < quizData.questions.length - 1) {
                showQuestion(currentQuestionIndex + 1);
            }
        }

        // 提交答题
        async function submitQuiz() {
            const confirmMessage = quizMode === 'immediate'
                ? '确定要结束答题吗？'
                : '确定要提交答案吗？提交后将无法修改。';

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                // 停止计时
                if (timerInterval) {
                    clearInterval(timerInterval);
                }

                const endTime = new Date();

                // 准备提交数据
                const answers = quizData.questions.map(question => ({
                    question_id: question.id,
                    selected_options: userAnswers[question.id] || []
                }));

                const submitData = {
                    bank_id: parseInt(bankId),
                    answers: answers,
                    start_time: startTime.toISOString(),
                    end_time: endTime.toISOString(),
                    shuffle_questions: shuffleQuestions,
                    shuffle_options: shuffleOptions
                };

                // 显示加载状态
                document.getElementById('loadingOverlay').style.display = 'flex';
                document.querySelector('.loading-content p').textContent = '正在提交答案...';

                // 提交答案
                const response = await apiPost('/answers/submit', submitData);

                if (response.success) {
                    // 跳转到结果页面
                    const resultData = encodeURIComponent(JSON.stringify(response.data));
                    window.location.href = `/result?data=${resultData}`;
                } else {
                    throw new Error(response.message || '提交失败');
                }

            } catch (error) {
                console.error('提交答案失败:', error);
                showMessage('提交答案失败: ' + error.message, 'error');
                document.getElementById('loadingOverlay').style.display = 'none';

                // 重新开始计时
                if (!timerInterval) {
                    timerInterval = setInterval(updateTimer, 1000);
                }
            }
        }

        // 显示解析
        function showExplanation(question) {
            const explanationSection = document.getElementById('explanationSection');
            const explanationText = document.getElementById('explanationText');
            const explanationImage = document.getElementById('explanationImage');

            if (question.explanation_text || question.explanation_image) {
                explanationSection.style.display = 'block';

                if (question.explanation_text) {
                    explanationText.textContent = question.explanation_text;
                    explanationText.style.display = 'block';
                } else {
                    explanationText.style.display = 'none';
                }

                if (question.explanation_image) {
                    explanationImage.src = question.explanation_image;
                    explanationImage.style.display = 'block';
                } else {
                    explanationImage.style.display = 'none';
                }
            } else {
                explanationSection.style.display = 'none';
            }

            // 重置解析展开状态
            const explanationToggle = document.getElementById('explanationToggle');
            const explanationContent = document.getElementById('explanationContent');
            explanationToggle.classList.remove('expanded');
            explanationContent.classList.remove('show');
        }

        // 切换解析显示
        function toggleExplanation() {
            const explanationToggle = document.getElementById('explanationToggle');
            const explanationContent = document.getElementById('explanationContent');

            explanationToggle.classList.toggle('expanded');
            explanationContent.classList.toggle('show');
        }

        // 显示答案反馈（即时反馈模式）
        function showAnswerFeedback(question) {
            const questionId = question.id;
            const userAnswer = userAnswers[questionId] || [];

            // 调试信息
            console.log('=== 答案反馈调试 ===');
            console.log('题目ID:', questionId);
            console.log('题目选项:', question.options);
            console.log('用户答案:', userAnswer);

            // 注意：MySQL返回的is_correct是数字（0或1），需要转换为布尔值
            const correctOptions = question.options
                .filter(opt => {
                    console.log(`选项 ${opt.option_label}: is_correct = ${opt.is_correct} (类型: ${typeof opt.is_correct})`);
                    return opt.is_correct === 1 || opt.is_correct === true;
                })
                .map(opt => opt.option_label);

            console.log('正确答案选项:', correctOptions);

            const isCorrect = JSON.stringify(userAnswer.sort()) === JSON.stringify(correctOptions.sort());

            console.log('用户答案排序:', userAnswer.sort());
            console.log('正确答案排序:', correctOptions.sort());
            console.log('是否正确:', isCorrect);

            const feedbackElement = document.getElementById('answerFeedback');
            const feedbackMessage = document.getElementById('feedbackMessage');
            const correctAnswerDisplay = document.getElementById('correctAnswerDisplay');

            feedbackElement.className = `answer-feedback show ${isCorrect ? 'correct' : 'incorrect'}`;

            if (isCorrect) {
                feedbackMessage.textContent = '✅ 回答正确！';
                correctAnswerDisplay.textContent = ''; // 清空正确答案显示
            } else {
                feedbackMessage.textContent = '❌ 回答错误';
                correctAnswerDisplay.textContent = `正确答案：${correctOptions.join(', ')}`;
            }
        }

        // 隐藏答案反馈
        function hideAnswerFeedback() {
            const feedbackElement = document.getElementById('answerFeedback');
            feedbackElement.classList.remove('show');
        }

        // 初始化答题卡
        function initializeAnswerCard() {
            if (!quizData || !quizData.questions || quizData.questions.length === 0) {
                console.log('题目数据未加载，跳过答题卡初始化');
                return;
            }

            const grid = document.getElementById('answerGrid');
            grid.innerHTML = '';

            console.log('初始化答题卡，题目数量:', quizData.questions.length);

            quizData.questions.forEach((question, index) => {
                const item = document.createElement('div');
                item.className = 'answer-item';
                item.textContent = index + 1;
                item.onclick = () => goToQuestion(index);
                item.id = `answer-item-${index}`;
                grid.appendChild(item);
            });

            updateAnswerCard();
        }

        // 更新答题卡状态
        function updateAnswerCard() {
            if (!quizData || !quizData.questions || quizData.questions.length === 0) {
                return;
            }

            const answeredCount = Object.values(userAnswers).filter(answer => answer.length > 0).length;
            const totalQuestions = quizData.questions.length;
            const unansweredCount = totalQuestions - answeredCount;

            // 更新统计信息
            document.getElementById('cardTotalQuestions').textContent = totalQuestions;
            document.getElementById('cardAnsweredCount').textContent = answeredCount;
            document.getElementById('cardUnansweredCount').textContent = unansweredCount;

            // 更新答题卡网格状态
            quizData.questions.forEach((question, index) => {
                const item = document.getElementById(`answer-item-${index}`);
                if (!item) return;

                // 清除所有状态类
                item.classList.remove('current', 'answered', 'unanswered');

                // 设置当前题目
                if (index === currentQuestionIndex) {
                    item.classList.add('current');
                }
                // 设置已答题状态
                else if (userAnswers[question.id] && userAnswers[question.id].length > 0) {
                    item.classList.add('answered');
                }
                // 设置未答题状态（仅在考试模式下显示）
                else if (quizMode === 'exam') {
                    item.classList.add('unanswered');
                }
            });
        }

        // 切换答题卡显示
        function toggleAnswerCard() {
            const card = document.getElementById('answerCard');
            if (card.style.display === 'none' || card.style.display === '') {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }

        // 跳转到指定题目
        function goToQuestion(questionIndex) {
            if (questionIndex >= 0 && questionIndex < quizData.questions.length && quizData.questions[questionIndex]) {
                console.log('跳转到题目:', questionIndex + 1, quizData.questions[questionIndex]);
                showQuestion(questionIndex); // 传递索引而不是题目对象
                // 关闭答题卡（可选）
                // toggleAnswerCard();
            } else {
                console.error('无效的题目索引:', questionIndex, '总题数:', quizData.questions.length);
            }
        }

        // 页面卸载时清理计时器
        window.addEventListener('beforeunload', function () {
            if (timerInterval) {
                clearInterval(timerInterval);
            }
        });
    </script>
</body>

</html>