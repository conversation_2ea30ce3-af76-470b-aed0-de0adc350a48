const mysql = require('mysql2');

// 数据库连接配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'question_bank_system',
    charset: 'utf8mb4',
    timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// 获取Promise版本的连接池
const promisePool = pool.promise();

// 初始化数据库和表结构
async function initDatabase() {
    try {
        // 创建数据库（如果不存在）
        const connection = mysql.createConnection({
            host: dbConfig.host,
            user: dbConfig.user,
            password: dbConfig.password,
            charset: dbConfig.charset
        });

        await connection.promise().execute(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
        console.log('数据库创建成功或已存在');
        connection.end();

        // 创建题库表
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS question_banks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL COMMENT '题库名称',
                description TEXT COMMENT '题库描述',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题库表'
        `);

        // 创建题目表
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                bank_id INT NOT NULL COMMENT '所属题库ID',
                question_text TEXT COMMENT '题目文字内容',
                question_image VARCHAR(500) COMMENT '题目图片路径',
                question_type ENUM('single', 'multiple') DEFAULT 'single' COMMENT '题目类型：单选、多选',
                explanation_text TEXT COMMENT '解析文字内容',
                explanation_image VARCHAR(500) COMMENT '解析图片路径',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_id) REFERENCES question_banks(id) ON DELETE CASCADE,
                INDEX idx_bank_id (bank_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目表'
        `);

        // 创建选项表
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS question_options (
                id INT AUTO_INCREMENT PRIMARY KEY,
                question_id INT NOT NULL COMMENT '所属题目ID',
                option_label CHAR(1) NOT NULL COMMENT '选项标签 A,B,C,D',
                option_text TEXT COMMENT '选项文字内容',
                option_image VARCHAR(500) COMMENT '选项图片路径',
                is_correct BOOLEAN DEFAULT FALSE COMMENT '是否为正确答案',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
                INDEX idx_question_id (question_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目选项表'
        `);

        // 创建答题记录表
        await promisePool.execute(`
            CREATE TABLE IF NOT EXISTS answer_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                bank_id INT NOT NULL COMMENT '题库ID',
                total_questions INT NOT NULL COMMENT '总题数',
                correct_answers INT NOT NULL COMMENT '正确答案数',
                score DECIMAL(5,2) NOT NULL COMMENT '得分',
                start_time TIMESTAMP NOT NULL COMMENT '开始时间',
                end_time TIMESTAMP NOT NULL COMMENT '结束时间',
                duration INT NOT NULL COMMENT '用时（秒）',
                shuffle_questions BOOLEAN DEFAULT FALSE COMMENT '是否打乱题目顺序',
                shuffle_options BOOLEAN DEFAULT FALSE COMMENT '是否打乱选项顺序',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bank_id) REFERENCES question_banks(id) ON DELETE CASCADE,
                INDEX idx_bank_id (bank_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='答题记录表'
        `);

        console.log('数据表创建成功');
    } catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    }
}

module.exports = {
    pool: promisePool,
    initDatabase
};
