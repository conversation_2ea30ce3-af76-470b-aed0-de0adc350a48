const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads/images');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名：时间戳 + 随机数 + 原扩展名
        const timestamp = Date.now();
        const random = Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, `${timestamp}_${random}${ext}`);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 检查文件类型
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
        return cb(null, true);
    } else {
        cb(new Error('只支持图片文件 (jpeg, jpg, png, gif, webp)'));
    }
};

// 配置multer
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB限制
    },
    fileFilter: fileFilter
});

// 单文件上传
router.post('/image', upload.single('image'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '没有上传文件'
            });
        }

        const fileUrl = `/uploads/images/${req.file.filename}`;
        
        res.json({
            success: true,
            message: '文件上传成功',
            data: {
                filename: req.file.filename,
                originalname: req.file.originalname,
                size: req.file.size,
                url: fileUrl,
                full_url: `${req.protocol}://${req.get('host')}${fileUrl}`
            }
        });
    } catch (error) {
        console.error('文件上传失败:', error);
        res.status(500).json({
            success: false,
            message: '文件上传失败'
        });
    }
});

// 多文件上传
router.post('/images', upload.array('images', 10), (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有上传文件'
            });
        }

        const files = req.files.map(file => {
            const fileUrl = `/uploads/images/${file.filename}`;
            return {
                filename: file.filename,
                originalname: file.originalname,
                size: file.size,
                url: fileUrl,
                full_url: `${req.protocol}://${req.get('host')}${fileUrl}`
            };
        });

        res.json({
            success: true,
            message: '文件上传成功',
            data: files
        });
    } catch (error) {
        console.error('文件上传失败:', error);
        res.status(500).json({
            success: false,
            message: '文件上传失败'
        });
    }
});

// 删除文件
router.delete('/image/:filename', (req, res) => {
    try {
        const filename = req.params.filename;
        const filePath = path.join(uploadDir, filename);

        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        // 删除文件
        fs.unlinkSync(filePath);

        res.json({
            success: true,
            message: '文件删除成功'
        });
    } catch (error) {
        console.error('文件删除失败:', error);
        res.status(500).json({
            success: false,
            message: '文件删除失败'
        });
    }
});

// 获取文件信息
router.get('/image/:filename', (req, res) => {
    try {
        const filename = req.params.filename;
        const filePath = path.join(uploadDir, filename);

        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

        const stats = fs.statSync(filePath);
        const fileUrl = `/uploads/images/${filename}`;

        res.json({
            success: true,
            data: {
                filename: filename,
                size: stats.size,
                created_at: stats.birthtime,
                modified_at: stats.mtime,
                url: fileUrl,
                full_url: `${req.protocol}://${req.get('host')}${fileUrl}`
            }
        });
    } catch (error) {
        console.error('获取文件信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取文件信息失败'
        });
    }
});

// 错误处理中间件
router.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: '文件大小超过限制（最大5MB）'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                message: '文件数量超过限制（最多10个）'
            });
        }
    }
    
    res.status(400).json({
        success: false,
        message: error.message || '文件上传失败'
    });
});

module.exports = router;
