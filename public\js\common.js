// 通用工具函数

// API基础URL
const API_BASE = '/api';

// 显示消息提示
function showMessage(message, type = 'info') {
    // 移除现有的消息
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建新的消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 显示加载状态
function showLoading(element) {
    if (element) {
        element.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
    }
}

// 隐藏加载状态
function hideLoading(element, content = '') {
    if (element) {
        element.innerHTML = content;
    }
}

// HTTP请求封装
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(API_BASE + url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || '请求失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// GET请求
async function apiGet(url) {
    return apiRequest(url, { method: 'GET' });
}

// POST请求
async function apiPost(url, data) {
    return apiRequest(url, {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

// PUT请求
async function apiPut(url, data) {
    return apiRequest(url, {
        method: 'PUT',
        body: JSON.stringify(data)
    });
}

// DELETE请求
async function apiDelete(url) {
    return apiRequest(url, { method: 'DELETE' });
}

// 文件上传
async function uploadFile(file) {
    try {
        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch(API_BASE + '/upload/image', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || '文件上传失败');
        }

        return data;
    } catch (error) {
        console.error('文件上传失败:', error);
        throw error;
    }
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化时长（秒转换为时分秒）
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}小时${minutes}分钟${secs}秒`;
    } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
    } else {
        return `${secs}秒`;
    }
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 分页组件
function createPagination(pagination, onPageChange) {
    const { current_page, total_pages, total_items } = pagination;

    if (total_pages <= 1) {
        return '';
    }

    let html = '<div class="pagination">';

    // 上一页
    if (current_page > 1) {
        html += `<a href="#" class="page-btn" data-page="${current_page - 1}">上一页</a>`;
    } else {
        html += `<span class="page-btn" disabled>上一页</span>`;
    }

    // 页码
    const startPage = Math.max(1, current_page - 2);
    const endPage = Math.min(total_pages, current_page + 2);

    if (startPage > 1) {
        html += `<a href="#" class="page-btn" data-page="1">1</a>`;
        if (startPage > 2) {
            html += `<span class="page-btn">...</span>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        if (i === current_page) {
            html += `<span class="page-btn active">${i}</span>`;
        } else {
            html += `<a href="#" class="page-btn" data-page="${i}">${i}</a>`;
        }
    }

    if (endPage < total_pages) {
        if (endPage < total_pages - 1) {
            html += `<span class="page-btn">...</span>`;
        }
        html += `<a href="#" class="page-btn" data-page="${total_pages}">${total_pages}</a>`;
    }

    // 下一页
    if (current_page < total_pages) {
        html += `<a href="#" class="page-btn" data-page="${current_page + 1}">下一页</a>`;
    } else {
        html += `<span class="page-btn" disabled>下一页</span>`;
    }

    html += '</div>';

    // 绑定点击事件
    setTimeout(() => {
        document.querySelectorAll('.page-btn[data-page]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                onPageChange(page);
            });
        });
    }, 0);

    return html;
}

// 表单验证
function validateForm(formElement) {
    const inputs = formElement.querySelectorAll('.form-control[required]');
    let isValid = true;

    inputs.forEach(input => {
        input.classList.remove('error');

        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        }
    });

    return isValid;
}

// 图片预览
function previewImage(input, previewElement) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function (e) {
            previewElement.src = e.target.result;
            previewElement.style.display = 'block';
        };

        reader.readAsDataURL(input.files[0]);
    }
}

// 清空表单
function clearForm(formElement) {
    const inputs = formElement.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        if (input.type === 'checkbox' || input.type === 'radio') {
            input.checked = false;
        } else {
            input.value = '';
        }
        input.classList.remove('error');
    });

    // 清空图片预览
    const previews = formElement.querySelectorAll('img[id*="preview"]');
    previews.forEach(preview => {
        preview.style.display = 'none';
        preview.src = '';
    });

    // 特别处理题目图片容器
    const questionImageContainer = document.getElementById('questionImageContainer');
    if (questionImageContainer) {
        questionImageContainer.style.display = 'none';
    }

    const questionImagePreview = document.getElementById('questionImagePreview');
    if (questionImagePreview) {
        questionImagePreview.src = '';
        questionImagePreview.style.display = 'none';
    }

    // 清理题目图片隐藏字段
    const questionImageInput = document.getElementById('questionImage');
    if (questionImageInput) {
        questionImageInput.value = '';
    }

    // 特别处理解析图片容器
    const explanationImageContainer = document.getElementById('explanationImageContainer');
    if (explanationImageContainer) {
        explanationImageContainer.style.display = 'none';
    }

    const explanationImagePreview = document.getElementById('explanationImagePreview');
    if (explanationImagePreview) {
        explanationImagePreview.src = '';
        explanationImagePreview.style.display = 'none';
    }

    // 清理所有选项图片容器
    const optionImageContainers = document.querySelectorAll('[id^="optionImageContainer_"]');
    optionImageContainers.forEach(container => {
        container.style.display = 'none';
    });

    const optionImagePreviews = document.querySelectorAll('[id^="optionImagePreview_"]');
    optionImagePreviews.forEach(preview => {
        preview.src = '';
        preview.style.display = 'none';
    });

    // 清空题目解析器
    const questionParser = document.getElementById('questionParser');
    if (questionParser) {
        questionParser.value = '';
    }
}

// 数组乱序
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

// 获取URL参数
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 设置URL参数
function setUrlParameter(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.pushState({}, '', url);
}

// 移动端检测
function isMobile() {
    return window.innerWidth <= 768;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function () {
    // 为所有确认删除按钮添加事件
    document.addEventListener('click', function (e) {
        if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
            e.preventDefault();
            const message = e.target.dataset.confirm || '确定要删除吗？';
            confirmAction(message, () => {
                // 如果是链接，跳转到href
                if (e.target.href) {
                    window.location.href = e.target.href;
                }
                // 如果有自定义删除函数，调用它
                if (e.target.dataset.deleteFunction) {
                    window[e.target.dataset.deleteFunction](e.target.dataset.id);
                }
            });
        }
    });

    // 响应式导航菜单
    const navbar = document.querySelector('.navbar');
    if (navbar && isMobile()) {
        navbar.classList.add('mobile');
    }
});
