const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// 获取所有题库列表（支持分页）
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        // 获取总数
        const [countResult] = await pool.execute('SELECT COUNT(*) as total FROM question_banks');
        const total = countResult[0].total;

        // 获取题库列表，包含题目数量
        const [banks] = await pool.execute(`
            SELECT
                qb.*,
                COUNT(q.id) as question_count
            FROM question_banks qb
            LEFT JOIN questions q ON qb.id = q.bank_id
            GROUP BY qb.id
            ORDER BY qb.created_at DESC
        `);

        res.json({
            success: true,
            data: {
                banks,
                pagination: {
                    current_page: page,
                    total_pages: Math.ceil(total / limit),
                    total_items: total,
                    items_per_page: limit
                }
            }
        });
    } catch (error) {
        console.error('获取题库列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取题库列表失败'
        });
    }
});

// 获取单个题库详情
router.get('/:id', async (req, res) => {
    try {
        const bankId = req.params.id;

        const [banks] = await pool.execute(`
            SELECT 
                qb.*,
                COUNT(q.id) as question_count
            FROM question_banks qb
            LEFT JOIN questions q ON qb.id = q.bank_id
            WHERE qb.id = ?
            GROUP BY qb.id
        `, [bankId]);

        if (banks.length === 0) {
            return res.status(404).json({
                success: false,
                message: '题库不存在'
            });
        }

        res.json({
            success: true,
            data: banks[0]
        });
    } catch (error) {
        console.error('获取题库详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取题库详情失败'
        });
    }
});

// 创建新题库
router.post('/', async (req, res) => {
    try {
        const { name, description } = req.body;

        if (!name || name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '题库名称不能为空'
            });
        }

        const [result] = await pool.execute(
            'INSERT INTO question_banks (name, description) VALUES (?, ?)',
            [name.trim(), description || '']
        );

        res.json({
            success: true,
            message: '题库创建成功',
            data: {
                id: result.insertId,
                name: name.trim(),
                description: description || ''
            }
        });
    } catch (error) {
        console.error('创建题库失败:', error);
        res.status(500).json({
            success: false,
            message: '创建题库失败'
        });
    }
});

// 更新题库
router.put('/:id', async (req, res) => {
    try {
        const bankId = req.params.id;
        const { name, description } = req.body;

        if (!name || name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '题库名称不能为空'
            });
        }

        const [result] = await pool.execute(
            'UPDATE question_banks SET name = ?, description = ? WHERE id = ?',
            [name.trim(), description || '', bankId]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '题库不存在'
            });
        }

        res.json({
            success: true,
            message: '题库更新成功'
        });
    } catch (error) {
        console.error('更新题库失败:', error);
        res.status(500).json({
            success: false,
            message: '更新题库失败'
        });
    }
});

// 删除题库
router.delete('/:id', async (req, res) => {
    try {
        const bankId = req.params.id;

        const [result] = await pool.execute('DELETE FROM question_banks WHERE id = ?', [bankId]);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '题库不存在'
            });
        }

        res.json({
            success: true,
            message: '题库删除成功'
        });
    } catch (error) {
        console.error('删除题库失败:', error);
        res.status(500).json({
            success: false,
            message: '删除题库失败'
        });
    }
});

module.exports = router;
