<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库系统 - 管理后台</title>
    <link rel="stylesheet" href="/css/common.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }

        .admin-header h1 {
            margin: 0;
            font-size: 2rem;
        }

        .admin-nav {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .admin-nav .container {
            display: flex;
            gap: 0;
        }

        .admin-nav button {
            background: none;
            border: none;
            padding: 1rem 2rem;
            cursor: pointer;
            font-size: 1rem;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .admin-nav button.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: #f8f9fa;
        }

        .admin-nav button:hover {
            background: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-box {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-box input {
            padding: 0.5rem;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            width: 250px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #495057;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }

        .close:hover {
            color: #fff;
            background: #dc3545;
            transform: scale(1.1);
        }

        .modal-body {
            padding: 2rem;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 1rem 2rem;
            border-top: 1px solid #dee2e6;
            border-radius: 0 0 10px 10px;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .image-upload {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .image-upload:hover {
            border-color: #667eea;
            background: #f8f9fa;
        }

        .image-upload.dragover {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            margin: 1rem auto;
            display: none;
            border-radius: 5px;
        }

        .option-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .option-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .option-header h4 {
            margin: 0;
            color: #495057;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .admin-nav .container {
                flex-direction: column;
            }

            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                flex-direction: column;
            }

            .search-box input {
                width: 100%;
            }

            .form-row {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 2% auto;
            }

            .modal-body {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <a href="/" class="logo">📚 题库系统</a>
            <ul class="nav-links">
                <li><a href="/">首页</a></li>
                <li><a href="/admin">管理后台</a></li>
            </ul>
        </div>
    </nav>

    <!-- 管理后台头部 -->
    <header class="admin-header">
        <div class="container">
            <h1>🛠️ 管理后台</h1>
        </div>
    </header>

    <!-- 管理导航 -->
    <nav class="admin-nav">
        <div class="container">
            <button class="tab-btn active" data-tab="dashboard">📊 数据统计</button>
            <button class="tab-btn" data-tab="banks">📚 题库管理</button>
            <button class="tab-btn" data-tab="questions">📝 题目管理</button>
            <button class="tab-btn" data-tab="records">📋 答题记录</button>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 数据统计标签页 -->
        <div id="dashboard" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalBanks">-</div>
                    <div class="stat-label">题库总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalQuestions">-</div>
                    <div class="stat-label">题目总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRecords">-</div>
                    <div class="stat-label">答题记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgScore">-</div>
                    <div class="stat-label">平均分数</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2>📈 最近答题记录</h2>
                </div>
                <div class="card-body">
                    <div id="recentRecords">
                        <div class="loading">
                            <div class="spinner"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 题库管理标签页 -->
        <div id="banks" class="tab-content">
            <div class="action-bar">
                <div class="search-box">
                    <input type="text" id="bankSearch" placeholder="搜索题库...">
                    <button class="btn btn-secondary" onclick="searchBanks()">搜索</button>
                </div>
                <button class="btn btn-primary" onclick="showBankModal()">➕ 新增题库</button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>题库名称</th>
                                    <th>描述</th>
                                    <th>题目数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="banksTableBody">
                                <tr>
                                    <td colspan="6">
                                        <div class="loading">
                                            <div class="spinner"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div id="banksPagination"></div>
        </div>

        <!-- 题目管理标签页 -->
        <div id="questions" class="tab-content">
            <div class="action-bar">
                <div class="search-box">
                    <select id="questionBankFilter" class="form-control" style="width: 200px;">
                        <option value="">选择题库</option>
                    </select>
                    <input type="text" id="questionSearch" placeholder="搜索题目...">
                    <button class="btn btn-secondary" onclick="searchQuestions()">搜索</button>
                </div>
                <button class="btn btn-primary" onclick="showQuestionModal()">➕ 新增题目</button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>题库</th>
                                    <th>题目内容</th>
                                    <th>类型</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="questionsTableBody">
                                <tr>
                                    <td colspan="6">
                                        <div class="loading">
                                            <div class="spinner"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div id="questionsPagination"></div>
        </div>

        <!-- 答题记录标签页 -->
        <div id="records" class="tab-content">
            <div class="action-bar">
                <div class="search-box">
                    <select id="recordBankFilter" class="form-control" style="width: 200px;">
                        <option value="">选择题库</option>
                    </select>
                    <button class="btn btn-secondary" onclick="searchRecords()">搜索</button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>题库</th>
                                    <th>总题数</th>
                                    <th>正确数</th>
                                    <th>得分</th>
                                    <th>用时</th>
                                    <th>答题时间</th>
                                </tr>
                            </thead>
                            <tbody id="recordsTableBody">
                                <tr>
                                    <td colspan="7">
                                        <div class="loading">
                                            <div class="spinner"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div id="recordsPagination"></div>
        </div>
    </main>

    <!-- 题库模态框 -->
    <div id="bankModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="bankModalTitle">新增题库</h3>
                <span class="close" onclick="closeBankModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="bankForm">
                    <input type="hidden" id="bankId">
                    <div class="form-group">
                        <label class="form-label" for="bankName">题库名称 *</label>
                        <input type="text" id="bankName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="bankDescription">题库描述</label>
                        <textarea id="bankDescription" class="form-control" rows="4"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeBankModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveBankForm()">保存</button>
            </div>
        </div>
    </div>

    <!-- 题目模态框 -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="questionModalTitle">新增题目</h3>
                <span class="close" onclick="closeQuestionModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="questionForm">
                    <input type="hidden" id="questionId">

                    <div class="form-group">
                        <label class="form-label">智能题目解析</label>
                        <textarea id="questionParser" class="form-control" rows="8"
                            placeholder="粘贴完整题目文本，包括题目内容和选项，例如：&#10;&#10;三相电路，要求负载各相电压均为电源相电压，元负载应接成(____)。&#10;A 星形有中线&#10;B 星形无中线&#10;C 三角形联接&#10;D 不能确定&#10;&#10;然后点击解析按钮自动填入表单"></textarea>
                        <div style="margin-top: 0.5rem;">
                            <button type="button" class="btn btn-primary" onclick="parseQuestionText()">🔍 解析题目</button>
                            <button type="button" class="btn btn-secondary" onclick="clearParser()">清空</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="questionBankId">所属题库 *</label>
                        <select id="questionBankId" class="form-control" required>
                            <option value="">请选择题库</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="questionType">题目类型</label>
                        <select id="questionType" class="form-control">
                            <option value="single">单选题</option>
                            <option value="multiple">多选题</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="questionText">题目内容（支持Ctrl+V粘贴图片）</label>
                        <textarea id="questionText" class="form-control" rows="4"
                            placeholder="输入题目文字内容，或按Ctrl+V粘贴图片"></textarea>
                        <div id="questionImageContainer" style="margin-top: 1rem; display: none;">
                            <img id="questionImagePreview" class="image-preview"
                                style="max-width: 300px; display: block;">
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeQuestionImage()"
                                style="margin-top: 0.5rem;">删除图片</button>
                        </div>
                        <input type="hidden" id="questionImage">
                        <input type="file" id="questionImageFile" accept="image/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label class="form-label">选项设置</label>
                        <div id="optionsContainer">
                            <!-- 选项将通过JavaScript动态生成 -->
                        </div>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="addOption()">➕ 添加选项</button>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="explanationText">解析内容（支持Ctrl+V粘贴图片）</label>
                        <textarea id="explanationText" class="form-control" rows="5"
                            placeholder="输入解析文字内容，或按Ctrl+V粘贴图片"></textarea>
                        <div id="explanationImageContainer" style="margin-top: 1rem; display: none;">
                            <img id="explanationImagePreview" class="image-preview"
                                style="max-width: 300px; display: block;">
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeExplanationImage()"
                                style="margin-top: 0.5rem;">删除图片</button>
                        </div>
                        <input type="hidden" id="explanationImage">
                        <input type="file" id="explanationImageFile" accept="image/*" style="display: none;">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeQuestionModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveQuestionForm()">保存</button>
            </div>
        </div>
    </div>

    <script src="/js/common.js"></script>
    <script src="/js/admin.js"></script>
    <script>
        // 全局变量
        let currentTab = 'dashboard';
        let currentBankPage = 1;
        let currentQuestionPage = 1;
        let currentRecordPage = 1;
        let editingBankId = null;
        let editingQuestionId = null;
        let optionCounter = 0;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('页面加载完成');
            try {
                initializePage();
                setupEventListeners();
                console.log('初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
            }
        });

        // 初始化页面
        async function initializePage() {
            console.log('开始初始化页面');
            try {
                await loadBankOptions();
                console.log('题库选项加载完成');
                await loadDashboardData();
                console.log('仪表板数据加载完成');
            } catch (error) {
                console.error('初始化页面失败:', error);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 标签页切换
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function () {
                    switchTab(this.dataset.tab);
                });
            });

            // 文件上传事件
            setupFileUpload('questionImageFile', 'questionImagePreview', 'questionImage');
            setupFileUpload('explanationImageFile', 'explanationImagePreview', 'explanationImage');

            // 设置题目内容的粘贴功能
            setupQuestionPasteUpload();

            // 防止模态框内容区域点击时关闭模态框
            document.addEventListener('click', function (event) {
                if (event.target.closest('.modal-content')) {
                    event.stopPropagation();
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Escape') {
                    const openModals = document.querySelectorAll('.modal[style*="block"]');
                    openModals.forEach(modal => {
                        modal.style.display = 'none';
                    });
                    hideModalClickHint();
                }
            });

            // 模态框点击外部关闭（需要双击确认）
            let modalClickCount = 0;
            let modalClickTimer = null;

            window.addEventListener('click', function (event) {
                if (event.target.classList.contains('modal')) {
                    modalClickCount++;

                    if (modalClickCount === 1) {
                        // 第一次点击，显示提示
                        showModalClickHint(event.target);
                        modalClickTimer = setTimeout(() => {
                            modalClickCount = 0;
                            hideModalClickHint();
                        }, 2000);
                    } else if (modalClickCount === 2) {
                        // 第二次点击，关闭模态框
                        clearTimeout(modalClickTimer);
                        modalClickCount = 0;
                        hideModalClickHint();
                        event.target.style.display = 'none';
                    }
                }
            });
        }

        // 切换标签页
        function switchTab(tabName) {
            console.log('切换到标签页:', tabName);
            try {
                // 更新按钮状态
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

                // 更新内容显示
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabName).classList.add('active');

                currentTab = tabName;

                // 加载对应数据
                switch (tabName) {
                    case 'dashboard':
                        console.log('加载仪表板数据');
                        loadDashboardData();
                        break;
                    case 'banks':
                        console.log('加载题库数据');
                        loadBanks();
                        break;
                    case 'questions':
                        console.log('加载题目数据');
                        loadQuestions();
                        break;
                    case 'records':
                        console.log('加载记录数据');
                        loadRecords();
                        break;
                }
            } catch (error) {
                console.error('切换标签页失败:', error);
            }
        }

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                // 加载统计数据
                const [banksResponse, recordsResponse] = await Promise.all([
                    apiGet('/banks?limit=1000'),
                    apiGet('/answers/records?limit=1000')
                ]);

                if (banksResponse.success) {
                    const banks = banksResponse.data.banks;
                    document.getElementById('totalBanks').textContent = banks.length;

                    const totalQuestions = banks.reduce((sum, bank) => sum + bank.question_count, 0);
                    document.getElementById('totalQuestions').textContent = totalQuestions;
                }

                if (recordsResponse.success) {
                    const records = recordsResponse.data.records;
                    document.getElementById('totalRecords').textContent = records.length;

                    if (records.length > 0) {
                        const avgScore = records.reduce((sum, record) => sum + record.score, 0) / records.length;
                        document.getElementById('avgScore').textContent = Math.round(avgScore * 10) / 10 + '%';
                    } else {
                        document.getElementById('avgScore').textContent = '0%';
                    }

                    // 显示最近记录
                    renderRecentRecords(records.slice(0, 10));
                }
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                showMessage('加载仪表板数据失败', 'error');
            }
        }

        // 渲染最近答题记录
        function renderRecentRecords(records) {
            const container = document.getElementById('recentRecords');

            if (records.length === 0) {
                container.innerHTML = '<p class="text-center">暂无答题记录</p>';
                return;
            }

            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>题库</th>
                                <th>得分</th>
                                <th>用时</th>
                                <th>答题时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${records.map(record => `
                                <tr>
                                    <td>${record.bank_name}</td>
                                    <td>${record.score}%</td>
                                    <td>${formatDuration(record.duration)}</td>
                                    <td>${formatDate(record.created_at)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // 加载题库选项（用于下拉框）
        async function loadBankOptions() {
            try {
                const response = await apiGet('/banks?limit=1000');
                if (response.success) {
                    const banks = response.data.banks;
                    const options = banks.map(bank =>
                        `<option value="${bank.id}">${bank.name}</option>`
                    ).join('');

                    // 更新所有题库选择框
                    document.getElementById('questionBankId').innerHTML = '<option value="">请选择题库</option>' + options;
                    document.getElementById('questionBankFilter').innerHTML = '<option value="">选择题库</option>' + options;
                    document.getElementById('recordBankFilter').innerHTML = '<option value="">选择题库</option>' + options;
                }
            } catch (error) {
                console.error('加载题库选项失败:', error);
            }
        }

        // ===== 题库管理相关函数 =====

        // 加载题库列表
        async function loadBanks(page = 1) {
            try {
                currentBankPage = page;
                showLoading(document.getElementById('banksTableBody'));

                const response = await apiGet(`/banks?page=${page}&limit=10`);

                if (response.success) {
                    renderBanksTable(response.data.banks);
                    renderBanksPagination(response.data.pagination);
                } else {
                    showMessage('加载题库列表失败', 'error');
                }
            } catch (error) {
                console.error('加载题库列表失败:', error);
                showMessage('加载题库列表失败: ' + error.message, 'error');
            }
        }

        // 渲染题库表格
        function renderBanksTable(banks) {
            const tbody = document.getElementById('banksTableBody');

            if (banks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无题库数据</td></tr>';
                return;
            }

            tbody.innerHTML = banks.map(bank => `
                <tr>
                    <td>${bank.id}</td>
                    <td>${bank.name}</td>
                    <td>${bank.description || '暂无描述'}</td>
                    <td>${bank.question_count}</td>
                    <td>${formatDate(bank.created_at)}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" onclick="editBank(${bank.id})">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteBank(${bank.id})"
                                data-confirm="确定要删除题库"${bank.name}"吗？这将同时删除所有相关题目！">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染题库分页
        function renderBanksPagination(pagination) {
            const container = document.getElementById('banksPagination');
            container.innerHTML = createPagination(pagination, loadBanks);
        }

        // 显示题库模态框
        function showBankModal(bankId = null) {
            editingBankId = bankId;
            const modal = document.getElementById('bankModal');
            const title = document.getElementById('bankModalTitle');

            if (bankId) {
                title.textContent = '编辑题库';
                loadBankData(bankId);
            } else {
                title.textContent = '新增题库';
                clearForm(document.getElementById('bankForm'));
            }

            modal.style.display = 'block';
        }

        // 加载题库数据到表单
        async function loadBankData(bankId) {
            try {
                const response = await apiGet(`/banks/${bankId}`);
                if (response.success) {
                    const bank = response.data;
                    document.getElementById('bankId').value = bank.id;
                    document.getElementById('bankName').value = bank.name;
                    document.getElementById('bankDescription').value = bank.description || '';
                }
            } catch (error) {
                console.error('加载题库数据失败:', error);
                showMessage('加载题库数据失败', 'error');
            }
        }

        // 关闭题库模态框
        function closeBankModal() {
            document.getElementById('bankModal').style.display = 'none';
            editingBankId = null;
        }

        // 保存题库表单
        async function saveBankForm() {
            const form = document.getElementById('bankForm');

            if (!validateForm(form)) {
                showMessage('请填写必填字段', 'error');
                return;
            }

            const formData = {
                name: document.getElementById('bankName').value.trim(),
                description: document.getElementById('bankDescription').value.trim()
            };

            try {
                let response;
                if (editingBankId) {
                    response = await apiPut(`/banks/${editingBankId}`, formData);
                } else {
                    response = await apiPost('/banks', formData);
                }

                if (response.success) {
                    showMessage(editingBankId ? '题库更新成功' : '题库创建成功', 'success');
                    closeBankModal();
                    loadBanks(currentBankPage);
                    loadBankOptions(); // 更新下拉框选项
                } else {
                    showMessage(response.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存题库失败:', error);
                showMessage('保存题库失败: ' + error.message, 'error');
            }
        }

        // 编辑题库
        function editBank(bankId) {
            showBankModal(bankId);
        }

        // 删除题库
        async function deleteBank(bankId) {
            // 二次确认
            const confirmed = confirm('⚠️ 确定要删除这个题库吗？\n\n⚠️ 警告：删除题库将同时删除该题库下的所有题目！\n\n此操作无法恢复，请谨慎操作！');
            if (!confirmed) {
                return; // 用户取消删除
            }

            try {
                const response = await apiDelete(`/banks/${bankId}`);
                if (response.success) {
                    showMessage('题库删除成功', 'success');
                    loadBanks(currentBankPage);
                    loadBankOptions(); // 更新下拉框选项
                } else {
                    showMessage(response.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除题库失败:', error);
                showMessage('删除题库失败: ' + error.message, 'error');
            }
        }

        // 搜索题库
        function searchBanks() {
            const searchTerm = document.getElementById('bankSearch').value.trim();
            // 这里可以实现搜索功能，暂时重新加载
            loadBanks(1);
        }

        // ===== 题目管理相关函数 =====

        // 加载题目列表
        async function loadQuestions(page = 1) {
            try {
                currentQuestionPage = page;
                const bankId = document.getElementById('questionBankFilter').value;
                showLoading(document.getElementById('questionsTableBody'));

                let url = `/questions/bank/0?page=${page}&limit=10`;
                if (bankId) {
                    url = `/questions/bank/${bankId}?page=${page}&limit=10`;
                }

                const response = await apiGet(url);

                if (response.success) {
                    renderQuestionsTable(response.data.questions);
                    renderQuestionsPagination(response.data.pagination);
                } else {
                    showMessage('加载题目列表失败', 'error');
                }
            } catch (error) {
                console.error('加载题目列表失败:', error);
                showMessage('加载题目列表失败: ' + error.message, 'error');
            }
        }

        // 渲染题目表格
        function renderQuestionsTable(questions) {
            const tbody = document.getElementById('questionsTableBody');

            if (questions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无题目数据</td></tr>';
                return;
            }

            tbody.innerHTML = questions.map(question => `
                <tr>
                    <td>${question.id}</td>
                    <td>${question.bank_name}</td>
                    <td>${truncateText(question.question_text || '图片题目', 50)}</td>
                    <td>${question.question_type === 'single' ? '单选' : '多选'}</td>
                    <td>${formatDate(question.created_at)}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" onclick="editQuestion(${question.id})">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteQuestion(${question.id})"
                                data-confirm="确定要删除这道题目吗？">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染题目分页
        function renderQuestionsPagination(pagination) {
            const container = document.getElementById('questionsPagination');
            container.innerHTML = createPagination(pagination, loadQuestions);
        }

        // 搜索题目
        function searchQuestions() {
            loadQuestions(1);
        }

        // ===== 答题记录管理相关函数 =====

        // 加载答题记录列表
        async function loadRecords(page = 1) {
            try {
                currentRecordPage = page;
                const bankId = document.getElementById('recordBankFilter').value;
                showLoading(document.getElementById('recordsTableBody'));

                let url = `/answers/records?page=${page}&limit=10`;
                if (bankId) {
                    url += `&bank_id=${bankId}`;
                }

                const response = await apiGet(url);

                if (response.success) {
                    renderRecordsTable(response.data.records);
                    renderRecordsPagination(response.data.pagination);
                } else {
                    showMessage('加载答题记录失败', 'error');
                }
            } catch (error) {
                console.error('加载答题记录失败:', error);
                showMessage('加载答题记录失败: ' + error.message, 'error');
            }
        }

        // 渲染答题记录表格
        function renderRecordsTable(records) {
            const tbody = document.getElementById('recordsTableBody');

            if (records.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无答题记录</td></tr>';
                return;
            }

            tbody.innerHTML = records.map(record => `
                <tr>
                    <td>${record.id}</td>
                    <td>${record.bank_name}</td>
                    <td>${record.total_questions}</td>
                    <td>${record.correct_answers}</td>
                    <td>${record.score}%</td>
                    <td>${formatDuration(record.duration)}</td>
                    <td>${formatDate(record.created_at)}</td>
                </tr>
            `).join('');
        }

        // 渲染答题记录分页
        function renderRecordsPagination(pagination) {
            const container = document.getElementById('recordsPagination');
            container.innerHTML = createPagination(pagination, loadRecords);
        }

        // 搜索答题记录
        function searchRecords() {
            loadRecords(1);
        }

        // 显示模态框点击提示
        function showModalClickHint(modalElement) {
            // 移除现有提示
            hideModalClickHint();

            const hint = document.createElement('div');
            hint.id = 'modalClickHint';
            hint.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 1rem 2rem;
                border-radius: 8px;
                font-size: 1rem;
                z-index: 10000;
                pointer-events: none;
                animation: fadeInOut 2s ease-in-out;
            `;
            hint.textContent = '再次点击空白区域关闭';

            document.body.appendChild(hint);

            // 添加CSS动画
            if (!document.getElementById('modalHintStyle')) {
                const style = document.createElement('style');
                style.id = 'modalHintStyle';
                style.textContent = `
                    @keyframes fadeInOut {
                        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                        20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // 隐藏模态框点击提示
        function hideModalClickHint() {
            const hint = document.getElementById('modalClickHint');
            if (hint) {
                hint.remove();
            }
        }

        // 删除题目图片
        function removeQuestionImage() {
            const preview = document.getElementById('questionImagePreview');
            const hiddenInput = document.getElementById('questionImage');
            const container = document.getElementById('questionImageContainer');

            if (preview) {
                preview.src = '';
                preview.style.display = 'none';
            }

            if (hiddenInput) {
                hiddenInput.value = '';
            }

            if (container) {
                container.style.display = 'none';
            }

            showMessage('题目图片已删除', 'success');
        }

        // 设置题目内容的粘贴上传功能
        function setupQuestionPasteUpload() {
            const questionTextarea = document.getElementById('questionText');

            if (!questionTextarea) return;

            questionTextarea.addEventListener('paste', async function (e) {
                const items = e.clipboardData.items;

                for (let i = 0; i < items.length; i++) {
                    const item = items[i];

                    if (item.type.indexOf('image') !== -1) {
                        e.preventDefault();

                        const file = item.getAsFile();
                        if (file) {
                            await handleQuestionImageUpload(file);
                            showMessage('图片已粘贴到题目中', 'success');
                        }
                        break;
                    }
                }
            });

            // 添加拖拽功能
            questionTextarea.addEventListener('dragover', function (e) {
                e.preventDefault();
                this.style.backgroundColor = '#f0f4ff';
            });

            questionTextarea.addEventListener('dragleave', function (e) {
                e.preventDefault();
                this.style.backgroundColor = '';
            });

            questionTextarea.addEventListener('drop', async function (e) {
                e.preventDefault();
                this.style.backgroundColor = '';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type.indexOf('image') !== -1) {
                        await handleQuestionImageUpload(file);
                        showMessage('图片已添加到题目中', 'success');
                    }
                }
            });
        }

        // 处理题目图片上传
        async function handleQuestionImageUpload(file) {
            try {
                const preview = document.getElementById('questionImagePreview');
                const hiddenInput = document.getElementById('questionImage');
                const container = document.getElementById('questionImageContainer');

                // 显示预览
                if (preview) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';

                        // 显示图片容器
                        if (container) {
                            container.style.display = 'block';
                        }
                    };
                    reader.readAsDataURL(file);
                }

                // 上传文件
                const response = await uploadFile(file);
                if (response.success) {
                    const imageUrl = response.data.url;

                    if (hiddenInput) {
                        hiddenInput.value = imageUrl;
                    }

                    showMessage('题目图片上传成功', 'success');
                } else {
                    showMessage('题目图片上传失败', 'error');
                }
            } catch (error) {
                console.error('题目图片上传失败:', error);
                showMessage('题目图片上传失败: ' + error.message, 'error');
            }
        }

        // 解析题目文本
        function parseQuestionText() {
            const parserText = document.getElementById('questionParser').value.trim();
            if (!parserText) {
                showMessage('请输入题目文本', 'error');
                return;
            }

            try {
                const parsed = parseQuestionFromText(parserText);
                if (parsed) {
                    fillQuestionForm(parsed);
                    showMessage('题目解析成功！', 'success');
                } else {
                    showMessage('题目格式无法识别，请检查格式', 'error');
                }
            } catch (error) {
                console.error('解析题目失败:', error);
                showMessage('解析失败: ' + error.message, 'error');
            }
        }

        // 清空解析器
        function clearParser() {
            document.getElementById('questionParser').value = '';
        }

        // 从文本中解析题目
        function parseQuestionFromText(text) {
            const lines = text.split('\n').map(line => line.trim()).filter(line => line);

            if (lines.length < 2) {
                throw new Error('题目内容太少，至少需要题目和选项');
            }

            console.log('解析的行数:', lines.length);
            console.log('所有行:', lines);

            // 详细显示每一行
            lines.forEach((line, index) => {
                console.log(`第${index + 1}行: "${line}"`);
            });

            // 查找选项（支持多种格式）
            const options = [];
            const optionPatterns = [
                /^([A-Z])\s*[.、]\s*(.+)$/,  // A. 或 A、
                /^([A-Z])\s+(.+)$/,          // A 空格
                /^([A-Z])\s*(.+)$/,          // A直接跟内容
                /^([A-Z])[）)]\s*(.+)$/,     // A) 或 A）
                /^([A-Z])[：:]\s*(.+)$/      // A: 或 A：
            ];

            // 单独的选项标签模式（只有A、B、C、D等）
            const singleLabelPattern = /^([A-Z])\s*$/;

            let questionLines = [];
            let optionStartIndex = -1;
            let i = 0;

            // 先找到第一个选项的位置
            while (i < lines.length) {
                let isOption = false;
                console.log(`检查第${i + 1}行: "${lines[i]}"`);

                // 检查是否是单独的选项标签
                const singleLabelMatch = lines[i].match(singleLabelPattern);
                if (singleLabelMatch && i + 1 < lines.length) {
                    console.log(`找到单独选项标签: ${singleLabelMatch[1]}`);
                    const nextLine = lines[i + 1];
                    console.log(`下一行内容: "${nextLine}"`);

                    // 检查下一行是否不是选项标签
                    if (!nextLine.match(singleLabelPattern) && !nextLine.match(/^[A-Z]\s*[.、）):：]/)) {
                        console.log(`组合选项: ${singleLabelMatch[1]} - ${nextLine}`);
                        if (optionStartIndex === -1) {
                            optionStartIndex = i;
                        }
                        options.push({
                            label: singleLabelMatch[1],
                            text: nextLine.trim()
                        });
                        isOption = true;
                        i += 2; // 跳过下一行，因为已经处理了
                        continue;
                    }
                }

                // 检查传统的选项格式
                if (!isOption) {
                    for (let j = 0; j < optionPatterns.length; j++) {
                        const pattern = optionPatterns[j];
                        const match = lines[i].match(pattern);
                        if (match) {
                            console.log(`匹配模式${j + 1}成功:`, match[1], match[2]);
                            if (optionStartIndex === -1) {
                                optionStartIndex = i;
                            }
                            options.push({
                                label: match[1],
                                text: match[2].trim()
                            });
                            isOption = true;
                            break;
                        }
                    }
                }

                // 如果还没找到选项，这行是题目内容
                if (!isOption && optionStartIndex === -1) {
                    questionLines.push(lines[i]);
                    console.log('添加到题目内容:', lines[i]);
                } else if (!isOption) {
                    console.log('跳过非选项行:', lines[i]);
                }

                i++;
            }

            if (options.length < 2) {
                console.error('解析失败，找到的选项:', options);
                throw new Error(`至少需要2个选项，当前只找到${options.length}个选项`);
            }

            // 合并题目内容
            const questionText = questionLines.join(' ');

            // 判断题目类型（根据选项数量和内容）
            const questionType = options.length > 4 ? 'multiple' : 'single';

            console.log('解析结果:', {
                questionText,
                questionType,
                options: options.length
            });

            return {
                questionText: questionText,
                questionType: questionType,
                options: options
            };
        }

        // 填充题目表单
        function fillQuestionForm(parsed) {
            // 填充题目内容
            document.getElementById('questionText').value = parsed.questionText;

            // 设置题目类型
            document.getElementById('questionType').value = parsed.questionType;

            // 清空现有选项
            document.getElementById('optionsContainer').innerHTML = '';
            optionCounter = 0;

            // 添加解析出的选项
            parsed.options.forEach((option, index) => {
                addOption(option.label, option.text, '', false);
            });

            // 如果选项少于4个，补充到4个
            while (optionCounter < 4) {
                const nextLabel = String.fromCharCode(65 + optionCounter); // A, B, C, D...
                addOption(nextLabel, '', '', false);
            }
        }

        // 截断文本工具函数
        function truncateText(text, maxLength) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }
    </script>
</body>

</html>