// 管理后台JavaScript功能

// ===== 题目管理相关函数 =====

// 加载题目列表
async function loadQuestions(page = 1) {
    try {
        currentQuestionPage = page;
        const bankId = document.getElementById('questionBankFilter').value;
        showLoading(document.getElementById('questionsTableBody'));

        let url = `/questions/bank/0?page=${page}&limit=10`;
        if (bankId) {
            url = `/questions/bank/${bankId}?page=${page}&limit=10`;
        }

        const response = await apiGet(url);

        if (response.success) {
            renderQuestionsTable(response.data.questions);
            renderQuestionsPagination(response.data.pagination);
        } else {
            showMessage('加载题目列表失败', 'error');
        }
    } catch (error) {
        console.error('加载题目列表失败:', error);
        showMessage('加载题目列表失败: ' + error.message, 'error');
    }
}

// 渲染题目表格
function renderQuestionsTable(questions) {
    const tbody = document.getElementById('questionsTableBody');

    if (questions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无题目数据</td></tr>';
        return;
    }

    tbody.innerHTML = questions.map(question => `
        <tr>
            <td>${question.id}</td>
            <td>${question.bank_name}</td>
            <td>${truncateText(question.question_text || '图片题目', 50)}</td>
            <td>${question.question_type === 'single' ? '单选' : '多选'}</td>
            <td>${formatDate(question.created_at)}</td>
            <td>
                <button class="btn btn-warning btn-sm" onclick="editQuestion(${question.id})">编辑</button>
                <button class="btn btn-danger btn-sm" onclick="deleteQuestion(${question.id})"
                        data-confirm="确定要删除这道题目吗？">删除</button>
            </td>
        </tr>
    `).join('');
}

// 渲染题目分页
function renderQuestionsPagination(pagination) {
    const container = document.getElementById('questionsPagination');
    container.innerHTML = createPagination(pagination, loadQuestions);
}

// 显示题目模态框
function showQuestionModal(questionId = null) {
    editingQuestionId = questionId;
    const modal = document.getElementById('questionModal');
    const title = document.getElementById('questionModalTitle');

    if (questionId) {
        title.innerHTML = '编辑题目 <small style="font-size: 0.7em; color: #666; font-weight: normal;">(ESC键或点击×关闭)</small>';
        loadQuestionData(questionId);
    } else {
        title.innerHTML = '新增题目 <small style="font-size: 0.7em; color: #666; font-weight: normal;">(ESC键或点击×关闭)</small>';
        clearForm(document.getElementById('questionForm'));
        initializeOptions();
        // 确保清理题目图片
        if (typeof removeQuestionImage === 'function') {
            removeQuestionImage();
        }
        // 确保清理解析图片
        if (typeof removeExplanationImage === 'function') {
            removeExplanationImage();
        }
    }

    modal.style.display = 'block';
}

// 加载题目数据到表单
async function loadQuestionData(questionId) {
    try {
        const response = await apiGet(`/questions/${questionId}`);
        if (response.success) {
            const question = response.data;

            // 填充基本信息
            document.getElementById('questionId').value = question.id;
            document.getElementById('questionBankId').value = question.bank_id;
            document.getElementById('questionType').value = question.question_type;
            document.getElementById('questionText').value = question.question_text || '';
            document.getElementById('explanationText').value = question.explanation_text || '';

            // 处理图片
            if (question.question_image) {
                document.getElementById('questionImage').value = question.question_image;
                const preview = document.getElementById('questionImagePreview');
                preview.src = question.question_image;
                preview.style.display = 'block';
            }

            if (question.explanation_image) {
                document.getElementById('explanationImage').value = question.explanation_image;
                const preview = document.getElementById('explanationImagePreview');
                const container = document.getElementById('explanationImageContainer');
                preview.src = question.explanation_image;
                preview.style.display = 'block';
                if (container) {
                    container.style.display = 'block';
                }
            }

            // 加载选项
            loadQuestionOptions(question.options);
        }
    } catch (error) {
        console.error('加载题目数据失败:', error);
        showMessage('加载题目数据失败', 'error');
    }
}

// 加载题目选项
function loadQuestionOptions(options) {
    const container = document.getElementById('optionsContainer');
    container.innerHTML = '';
    optionCounter = 0;

    options.forEach((option, index) => {
        addOption(option.option_label, option.option_text, option.option_image, option.is_correct);
    });

    // 如果没有选项，添加默认的A、B选项
    if (options.length === 0) {
        initializeOptions();
    }
}

// 初始化选项（添加默认A、B选项）
function initializeOptions() {
    const container = document.getElementById('optionsContainer');
    container.innerHTML = '';
    optionCounter = 0;

    addOption('A');
    addOption('B');
}

// 添加选项
function addOption(label = '', text = '', image = '', isCorrect = false) {
    const container = document.getElementById('optionsContainer');
    const optionId = ++optionCounter;
    const optionLabel = label || String.fromCharCode(64 + optionId); // A, B, C, D...

    const optionHtml = `
        <div class="option-item" id="option_${optionId}">
            <div class="option-header">
                <h4>选项 ${optionLabel}</h4>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeOption(${optionId})">删除</button>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">选项标签</label>
                    <input type="text" class="form-control option-label" value="${optionLabel}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" class="option-correct" ${isCorrect ? 'checked' : ''}> 正确答案
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">选项内容（支持Ctrl+V粘贴图片）</label>
                <textarea class="form-control option-text" rows="3" id="optionText_${optionId}" placeholder="输入选项文字内容，或按Ctrl+V粘贴图片">${text}</textarea>
                <div id="optionImageContainer_${optionId}" style="margin-top: 1rem; display: ${image ? 'block' : 'none'};">
                    <img id="optionImagePreview_${optionId}" class="image-preview" style="max-width: 200px; display: block;" ${image ? `src="${image}"` : ''}>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeOptionImage(${optionId})" style="margin-top: 0.5rem;">删除图片</button>
                </div>
                <input type="hidden" class="option-image" value="${image}">
                <input type="file" id="optionImageFile_${optionId}" accept="image/*" style="display: none;">
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', optionHtml);

    // 设置文件上传事件
    setupFileUpload(`optionImageFile_${optionId}`, `optionImagePreview_${optionId}`, null, (url) => {
        document.querySelector(`#option_${optionId} .option-image`).value = url;
        // 显示图片容器
        const imageContainer = document.getElementById(`optionImageContainer_${optionId}`);
        if (imageContainer) {
            imageContainer.style.display = 'block';
        }
    });

    // 设置选项内容的粘贴功能
    setupOptionPasteUpload(optionId);
}

// 删除选项
function removeOption(optionId) {
    const optionElement = document.getElementById(`option_${optionId}`);
    if (optionElement) {
        optionElement.remove();
        updateOptionLabels();
    }
}

// 删除选项图片
function removeOptionImage(optionId) {
    const preview = document.getElementById(`optionImagePreview_${optionId}`);
    const hiddenInput = document.querySelector(`#option_${optionId} .option-image`);
    const container = document.getElementById(`optionImageContainer_${optionId}`);

    if (preview) {
        preview.src = '';
        preview.style.display = 'none';
    }

    if (hiddenInput) {
        hiddenInput.value = '';
    }

    if (container) {
        container.style.display = 'none';
    }

    showMessage('选项图片已删除', 'success');
}

// 设置选项内容的粘贴上传功能
function setupOptionPasteUpload(optionId) {
    const optionTextarea = document.getElementById(`optionText_${optionId}`);

    if (!optionTextarea) return;

    optionTextarea.addEventListener('paste', async function (e) {
        const items = e.clipboardData.items;

        for (let i = 0; i < items.length; i++) {
            const item = items[i];

            if (item.type.indexOf('image') !== -1) {
                e.preventDefault();

                const file = item.getAsFile();
                if (file) {
                    await handleOptionImageUpload(file, optionId);
                    showMessage('图片已粘贴到选项中', 'success');
                }
                break;
            }
        }
    });

    // 添加拖拽功能
    optionTextarea.addEventListener('dragover', function (e) {
        e.preventDefault();
        this.style.backgroundColor = '#f0f4ff';
    });

    optionTextarea.addEventListener('dragleave', function (e) {
        e.preventDefault();
        this.style.backgroundColor = '';
    });

    optionTextarea.addEventListener('drop', async function (e) {
        e.preventDefault();
        this.style.backgroundColor = '';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type.indexOf('image') !== -1) {
                await handleOptionImageUpload(file, optionId);
                showMessage('图片已添加到选项中', 'success');
            }
        }
    });
}

// 处理选项图片上传
async function handleOptionImageUpload(file, optionId) {
    try {
        const preview = document.getElementById(`optionImagePreview_${optionId}`);
        const hiddenInput = document.querySelector(`#option_${optionId} .option-image`);
        const container = document.getElementById(`optionImageContainer_${optionId}`);

        // 显示预览
        if (preview) {
            const reader = new FileReader();
            reader.onload = function (e) {
                preview.src = e.target.result;
                preview.style.display = 'block';

                // 显示图片容器
                if (container) {
                    container.style.display = 'block';
                }
            };
            reader.readAsDataURL(file);
        }

        // 上传文件
        const response = await uploadFile(file);
        if (response.success) {
            const imageUrl = response.data.url;

            if (hiddenInput) {
                hiddenInput.value = imageUrl;
            }

            showMessage('选项图片上传成功', 'success');
        } else {
            showMessage('选项图片上传失败', 'error');
        }
    } catch (error) {
        console.error('选项图片上传失败:', error);
        showMessage('选项图片上传失败: ' + error.message, 'error');
    }
}

// 更新选项标签
function updateOptionLabels() {
    const options = document.querySelectorAll('.option-item');
    options.forEach((option, index) => {
        const label = String.fromCharCode(65 + index); // A, B, C, D...
        option.querySelector('.option-label').value = label;
        option.querySelector('h4').textContent = `选项 ${label}`;
    });
}

// 关闭题目模态框
function closeQuestionModal() {
    document.getElementById('questionModal').style.display = 'none';
    editingQuestionId = null;
}

// 保存题目表单
async function saveQuestionForm() {
    const form = document.getElementById('questionForm');

    // 验证基本字段
    const bankId = document.getElementById('questionBankId').value;
    const questionText = document.getElementById('questionText').value.trim();
    const questionImage = document.getElementById('questionImage').value;

    if (!bankId) {
        showMessage('请选择题库', 'error');
        return;
    }

    if (!questionText && !questionImage) {
        showMessage('题目内容不能为空', 'error');
        return;
    }

    // 收集选项数据
    const options = [];
    const optionElements = document.querySelectorAll('.option-item');

    if (optionElements.length < 2) {
        showMessage('至少需要2个选项', 'error');
        return;
    }

    let hasCorrectAnswer = false;

    optionElements.forEach((element, index) => {
        const label = element.querySelector('.option-label').value;
        const text = element.querySelector('.option-text').value.trim();
        const image = element.querySelector('.option-image').value;
        const isCorrect = element.querySelector('.option-correct').checked;

        if (isCorrect) hasCorrectAnswer = true;

        options.push({
            option_label: label,
            option_text: text,
            option_image: image,
            is_correct: isCorrect
        });
    });

    if (!hasCorrectAnswer) {
        showMessage('必须设置至少一个正确答案', 'error');
        return;
    }

    const formData = {
        bank_id: bankId,
        question_text: questionText,
        question_image: questionImage,
        question_type: document.getElementById('questionType').value,
        explanation_text: document.getElementById('explanationText').value.trim(),
        explanation_image: document.getElementById('explanationImage').value,
        options: options
    };

    try {
        let response;
        if (editingQuestionId) {
            response = await apiPut(`/questions/${editingQuestionId}`, formData);
        } else {
            response = await apiPost('/questions', formData);
        }

        if (response.success) {
            showMessage(editingQuestionId ? '题目更新成功' : '题目创建成功', 'success');
            closeQuestionModal();
            loadQuestions(currentQuestionPage);
        } else {
            showMessage(response.message || '保存失败', 'error');
        }
    } catch (error) {
        console.error('保存题目失败:', error);
        showMessage('保存题目失败: ' + error.message, 'error');
    }
}

// 编辑题目
function editQuestion(questionId) {
    showQuestionModal(questionId);
}

// 删除题目
async function deleteQuestion(questionId) {
    // 二次确认
    const confirmed = confirm('⚠️ 确定要删除这道题目吗？\n\n删除后将无法恢复，请谨慎操作！');
    if (!confirmed) {
        return; // 用户取消删除
    }

    try {
        const response = await apiDelete(`/questions/${questionId}`);
        if (response.success) {
            showMessage('题目删除成功', 'success');
            loadQuestions(currentQuestionPage);
        } else {
            showMessage(response.message || '删除失败', 'error');
        }
    } catch (error) {
        console.error('删除题目失败:', error);
        showMessage('删除题目失败: ' + error.message, 'error');
    }
}

// 搜索题目
function searchQuestions() {
    loadQuestions(1);
}

// ===== 答题记录管理相关函数 =====

// 加载答题记录列表
async function loadRecords(page = 1) {
    try {
        currentRecordPage = page;
        const bankId = document.getElementById('recordBankFilter').value;
        showLoading(document.getElementById('recordsTableBody'));

        let url = `/answers/records?page=${page}&limit=10`;
        if (bankId) {
            url += `&bank_id=${bankId}`;
        }

        const response = await apiGet(url);

        if (response.success) {
            renderRecordsTable(response.data.records);
            renderRecordsPagination(response.data.pagination);
        } else {
            showMessage('加载答题记录失败', 'error');
        }
    } catch (error) {
        console.error('加载答题记录失败:', error);
        showMessage('加载答题记录失败: ' + error.message, 'error');
    }
}

// 渲染答题记录表格
function renderRecordsTable(records) {
    const tbody = document.getElementById('recordsTableBody');

    if (records.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无答题记录</td></tr>';
        return;
    }

    tbody.innerHTML = records.map(record => `
        <tr>
            <td>${record.id}</td>
            <td>${record.bank_name}</td>
            <td>${record.total_questions}</td>
            <td>${record.correct_answers}</td>
            <td>${record.score}%</td>
            <td>${formatDuration(record.duration)}</td>
            <td>${formatDate(record.created_at)}</td>
        </tr>
    `).join('');
}

// 渲染答题记录分页
function renderRecordsPagination(pagination) {
    const container = document.getElementById('recordsPagination');
    container.innerHTML = createPagination(pagination, loadRecords);
}

// 搜索答题记录
function searchRecords() {
    loadRecords(1);
}

// ===== 工具函数 =====

// 截断文本
function truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}



// 设置文件上传
function setupFileUpload(fileInputId, previewId, hiddenInputId, callback) {
    const fileInput = document.getElementById(fileInputId);
    const preview = document.getElementById(previewId);
    const hiddenInput = hiddenInputId ? document.getElementById(hiddenInputId) : null;

    if (!fileInput) return;

    fileInput.addEventListener('change', async function (e) {
        const file = e.target.files[0];
        if (!file) return;

        await handleImageUpload(file, preview, hiddenInput, callback);
    });

    // 添加粘贴功能（仅对解析图片）
    if (fileInputId === 'explanationImageFile') {
        setupExplanationPasteUpload(preview, hiddenInput, callback);
    }
}

// 处理图片上传
async function handleImageUpload(file, preview, hiddenInput, callback) {
    try {
        // 显示预览
        if (preview) {
            const reader = new FileReader();
            reader.onload = function (e) {
                preview.src = e.target.result;
                preview.style.display = 'block';

                // 如果是解析图片，显示图片容器
                if (preview.id === 'explanationImagePreview') {
                    const container = document.getElementById('explanationImageContainer');
                    if (container) {
                        container.style.display = 'block';
                    }
                }
            };
            reader.readAsDataURL(file);
        }

        // 上传文件
        const response = await uploadFile(file);
        if (response.success) {
            const imageUrl = response.data.url;

            if (hiddenInput) {
                hiddenInput.value = imageUrl;
            }

            if (callback) {
                callback(imageUrl);
            }

            showMessage('图片上传成功', 'success');
        } else {
            showMessage('图片上传失败', 'error');
        }
    } catch (error) {
        console.error('图片上传失败:', error);
        showMessage('图片上传失败: ' + error.message, 'error');
    }
}

// 设置解析内容的粘贴上传功能
function setupExplanationPasteUpload(preview, hiddenInput, callback) {
    const explanationTextarea = document.getElementById('explanationText');

    if (!explanationTextarea) return;

    explanationTextarea.addEventListener('paste', async function (e) {
        const items = e.clipboardData.items;

        for (let i = 0; i < items.length; i++) {
            const item = items[i];

            if (item.type.indexOf('image') !== -1) {
                e.preventDefault();

                const file = item.getAsFile();
                if (file) {
                    await handleImageUpload(file, preview, hiddenInput, callback);
                    showMessage('图片已粘贴到解析中', 'success');
                }
                break;
            }
        }
    });

    // 添加拖拽功能
    explanationTextarea.addEventListener('dragover', function (e) {
        e.preventDefault();
        this.style.backgroundColor = '#f0f4ff';
    });

    explanationTextarea.addEventListener('dragleave', function (e) {
        e.preventDefault();
        this.style.backgroundColor = '';
    });

    explanationTextarea.addEventListener('drop', async function (e) {
        e.preventDefault();
        this.style.backgroundColor = '';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type.indexOf('image') !== -1) {
                await handleImageUpload(file, preview, hiddenInput, callback);
                showMessage('图片已添加到解析中', 'success');
            }
        }
    });
}

// 删除解析图片
function removeExplanationImage() {
    const preview = document.getElementById('explanationImagePreview');
    const hiddenInput = document.getElementById('explanationImage');
    const container = document.getElementById('explanationImageContainer');

    if (preview) {
        preview.src = '';
        preview.style.display = 'none';
    }

    if (hiddenInput) {
        hiddenInput.value = '';
    }

    if (container) {
        container.style.display = 'none';
    }

    showMessage('解析图片已删除', 'success');
}
