<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库系统 - 答题结果</title>
    <link rel="stylesheet" href="/css/common.css">
    <style>
        .result-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 0 20px;
        }

        .result-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }

        .result-header h1 {
            margin: 0 0 1rem 0;
            font-size: 2.5rem;
        }

        .score-display {
            font-size: 4rem;
            font-weight: bold;
            margin: 1rem 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .score-label {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .result-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .result-details {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .details-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .details-header h2 {
            margin: 0;
            color: #495057;
        }

        .details-content {
            padding: 2rem;
        }

        .question-result {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .question-result.correct {
            border-color: #28a745;
            background: #f8fff9;
        }

        .question-result.incorrect {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .question-result-header {
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .question-result.correct .question-result-header {
            background: #d4edda;
            color: #155724;
        }

        .question-result.incorrect .question-result-header {
            background: #f8d7da;
            color: #721c24;
        }

        .question-number {
            font-weight: bold;
        }

        .result-icon {
            font-size: 1.2rem;
        }

        .question-result-body {
            padding: 1.5rem;
        }

        .answer-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 1rem;
        }

        .answer-section {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
        }

        .answer-section h4 {
            margin: 0 0 0.5rem 0;
            color: #495057;
        }

        .answer-options {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .answer-options li {
            padding: 0.25rem 0;
        }

        .correct-answer {
            color: #28a745;
            font-weight: bold;
        }

        .incorrect-answer {
            color: #dc3545;
        }

        .action-buttons {
            text-align: center;
            margin: 3rem 0;
        }

        .action-buttons .btn {
            margin: 0 1rem;
        }

        .congratulations {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 15px;
            margin-bottom: 2rem;
        }

        .congratulations.excellent {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }

        .congratulations h3 {
            margin: 0 0 1rem 0;
            color: #856404;
        }

        .congratulations.excellent h3 {
            color: #155724;
        }

        @media (max-width: 768px) {
            .result-container {
                padding: 0 15px;
            }
            
            .result-header {
                padding: 2rem 1rem;
            }
            
            .result-header h1 {
                font-size: 2rem;
            }
            
            .score-display {
                font-size: 3rem;
            }
            
            .result-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .answer-comparison {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .action-buttons .btn {
                display: block;
                margin: 0.5rem 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <a href="/" class="logo">📚 题库系统</a>
            <ul class="nav-links">
                <li><a href="/">首页</a></li>
                <li><a href="/admin">管理后台</a></li>
            </ul>
        </div>
    </nav>

    <!-- 结果容器 -->
    <div class="result-container">
        <!-- 结果头部 -->
        <div id="resultHeader" class="result-header">
            <h1>🎉 答题完成</h1>
            <div class="score-display" id="scoreDisplay">0%</div>
            <div class="score-label">最终得分</div>
        </div>

        <!-- 祝贺信息 -->
        <div id="congratulations" class="congratulations" style="display: none;">
            <h3 id="congratulationsTitle">恭喜你！</h3>
            <p id="congratulationsMessage">你的表现很棒！</p>
        </div>

        <!-- 统计信息 -->
        <div class="result-stats">
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-value" id="totalQuestions">0</div>
                <div class="stat-label">总题数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-value" id="correctAnswers">0</div>
                <div class="stat-label">正确题数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">❌</div>
                <div class="stat-value" id="incorrectAnswers">0</div>
                <div class="stat-label">错误题数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏱️</div>
                <div class="stat-value" id="duration">0</div>
                <div class="stat-label">答题用时</div>
            </div>
        </div>

        <!-- 详细结果 -->
        <div class="result-details">
            <div class="details-header">
                <h2>📋 详细结果</h2>
            </div>
            <div class="details-content" id="detailsContent">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <a href="/" class="btn btn-primary btn-lg">返回首页</a>
            <button class="btn btn-secondary btn-lg" onclick="retakeQuiz()">重新答题</button>
            <button class="btn btn-success btn-lg" onclick="shareResult()">分享结果</button>
        </div>
    </div>

    <script src="/js/common.js"></script>
    <script>
        let resultData = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadResultData();
        });

        // 加载结果数据
        function loadResultData() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const dataParam = urlParams.get('data');
                
                if (!dataParam) {
                    showMessage('无效的结果数据', 'error');
                    setTimeout(() => window.location.href = '/', 2000);
                    return;
                }

                resultData = JSON.parse(decodeURIComponent(dataParam));
                displayResult();
            } catch (error) {
                console.error('加载结果数据失败:', error);
                showMessage('加载结果数据失败', 'error');
                setTimeout(() => window.location.href = '/', 2000);
            }
        }

        // 显示结果
        function displayResult() {
            if (!resultData) return;

            // 显示基本信息
            document.getElementById('scoreDisplay').textContent = resultData.score + '%';
            document.getElementById('totalQuestions').textContent = resultData.total_questions;
            document.getElementById('correctAnswers').textContent = resultData.correct_answers;
            document.getElementById('incorrectAnswers').textContent = resultData.total_questions - resultData.correct_answers;
            document.getElementById('duration').textContent = formatDuration(resultData.duration);

            // 显示祝贺信息
            showCongratulations();

            // 显示详细结果
            displayDetailedResults();
        }

        // 显示祝贺信息
        function showCongratulations() {
            const score = resultData.score;
            const congratulations = document.getElementById('congratulations');
            const title = document.getElementById('congratulationsTitle');
            const message = document.getElementById('congratulationsMessage');

            let titleText, messageText, className;

            if (score >= 90) {
                titleText = '🏆 优秀！';
                messageText = '你的表现非常出色，继续保持！';
                className = 'excellent';
            } else if (score >= 80) {
                titleText = '🎖️ 良好！';
                messageText = '你的表现很不错，再接再厉！';
                className = '';
            } else if (score >= 60) {
                titleText = '👍 及格！';
                messageText = '你已经掌握了基本知识，继续努力！';
                className = '';
            } else {
                titleText = '💪 加油！';
                messageText = '不要气馁，多练习就会进步的！';
                className = '';
            }

            title.textContent = titleText;
            message.textContent = messageText;
            congratulations.className = `congratulations ${className}`;
            congratulations.style.display = 'block';
        }

        // 显示详细结果
        async function displayDetailedResults() {
            const container = document.getElementById('detailsContent');
            
            if (!resultData.results || resultData.results.length === 0) {
                container.innerHTML = '<p class="text-center">暂无详细结果</p>';
                return;
            }

            try {
                // 获取题目详细信息
                const questionDetails = await Promise.all(
                    resultData.results.map(result => 
                        apiGet(`/questions/${result.question_id}`)
                    )
                );

                let html = '';
                resultData.results.forEach((result, index) => {
                    const questionData = questionDetails[index];
                    if (!questionData.success) return;

                    const question = questionData.data;
                    const isCorrect = result.is_correct;
                    
                    html += `
                        <div class="question-result ${isCorrect ? 'correct' : 'incorrect'}">
                            <div class="question-result-header">
                                <span class="question-number">第 ${index + 1} 题</span>
                                <span class="result-icon">${isCorrect ? '✅' : '❌'}</span>
                            </div>
                            <div class="question-result-body">
                                <div class="question-text">
                                    ${question.question_text || ''}
                                    ${question.question_image ? `<br><img src="${question.question_image}" class="question-image" style="max-width: 300px; margin-top: 0.5rem;">` : ''}
                                </div>
                                
                                <div class="answer-comparison">
                                    <div class="answer-section">
                                        <h4>你的答案</h4>
                                        <ul class="answer-options">
                                            ${result.selected_options.length > 0 
                                                ? result.selected_options.map(opt => `<li class="${isCorrect ? 'correct-answer' : 'incorrect-answer'}">${opt}</li>`).join('')
                                                : '<li class="incorrect-answer">未作答</li>'
                                            }
                                        </ul>
                                    </div>
                                    <div class="answer-section">
                                        <h4>正确答案</h4>
                                        <ul class="answer-options">
                                            ${result.correct_options.map(opt => `<li class="correct-answer">${opt}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                                
                                ${question.explanation_text || question.explanation_image ? `
                                    <div class="explanation" style="margin-top: 1rem; padding: 1rem; background: #f0f4ff; border-radius: 8px;">
                                        <h4 style="margin: 0 0 0.5rem 0; color: #495057;">解析</h4>
                                        ${question.explanation_text ? `<p>${question.explanation_text}</p>` : ''}
                                        ${question.explanation_image ? `<img src="${question.explanation_image}" style="max-width: 100%; margin-top: 0.5rem;">` : ''}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;
            } catch (error) {
                console.error('加载详细结果失败:', error);
                container.innerHTML = '<p class="text-center">加载详细结果失败</p>';
            }
        }

        // 重新答题
        function retakeQuiz() {
            if (resultData && resultData.bank_name) {
                // 这里需要获取题库ID，简化处理直接返回首页
                window.location.href = '/';
            }
        }

        // 分享结果
        function shareResult() {
            const shareText = `我在"${resultData.bank_name}"题库中获得了${resultData.score}%的成绩！总共${resultData.total_questions}道题，答对了${resultData.correct_answers}道。`;
            
            if (navigator.share) {
                navigator.share({
                    title: '题库答题结果',
                    text: shareText,
                    url: window.location.href
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    showMessage('结果已复制到剪贴板', 'success');
                }).catch(() => {
                    showMessage('分享功能不支持', 'warning');
                });
            }
        }
    </script>
</body>
</html>
