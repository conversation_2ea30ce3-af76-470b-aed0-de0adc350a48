const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// 获取指定题库的题目列表（支持分页）
router.get('/bank/:bankId', async (req, res) => {
    try {
        const bankId = req.params.bankId;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        let whereClause = '';
        let params = [];

        // 如果bankId为0，获取所有题目；否则获取指定题库的题目
        if (bankId !== '0') {
            whereClause = 'WHERE q.bank_id = ?';
            params.push(bankId);
        }

        // 获取总数
        const [countResult] = await pool.execute(
            `SELECT COUNT(*) as total FROM questions q ${whereClause}`,
            params
        );
        const total = countResult[0].total;

        // 获取题目列表
        const [questions] = await pool.execute(`
            SELECT
                q.*,
                qb.name as bank_name
            FROM questions q
            JOIN question_banks qb ON q.bank_id = qb.id
            ${whereClause}
            ORDER BY q.created_at DESC
        `, params);

        // 获取每个题目的选项
        for (let question of questions) {
            const [options] = await pool.execute(
                'SELECT * FROM question_options WHERE question_id = ? ORDER BY option_label',
                [question.id]
            );
            question.options = options;
        }

        res.json({
            success: true,
            data: {
                questions,
                pagination: {
                    current_page: page,
                    total_pages: Math.ceil(total / limit),
                    total_items: total,
                    items_per_page: limit
                }
            }
        });
    } catch (error) {
        console.error('获取题目列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取题目列表失败'
        });
    }
});

// 获取单个题目详情
router.get('/:id', async (req, res) => {
    try {
        const questionId = req.params.id;

        const [questions] = await pool.execute(`
            SELECT 
                q.*,
                qb.name as bank_name
            FROM questions q
            JOIN question_banks qb ON q.bank_id = qb.id
            WHERE q.id = ?
        `, [questionId]);

        if (questions.length === 0) {
            return res.status(404).json({
                success: false,
                message: '题目不存在'
            });
        }

        const question = questions[0];

        // 获取选项
        const [options] = await pool.execute(
            'SELECT * FROM question_options WHERE question_id = ? ORDER BY option_label',
            [questionId]
        );
        question.options = options;

        res.json({
            success: true,
            data: question
        });
    } catch (error) {
        console.error('获取题目详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取题目详情失败'
        });
    }
});

// 创建新题目
router.post('/', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const {
            bank_id,
            question_text,
            question_image,
            question_type,
            explanation_text,
            explanation_image,
            options
        } = req.body;

        // 验证必填字段
        if (!bank_id) {
            return res.status(400).json({
                success: false,
                message: '题库ID不能为空'
            });
        }

        if (!question_text && !question_image) {
            return res.status(400).json({
                success: false,
                message: '题目内容不能为空'
            });
        }

        if (!options || !Array.isArray(options) || options.length < 2) {
            return res.status(400).json({
                success: false,
                message: '至少需要2个选项'
            });
        }

        // 检查是否有正确答案
        const hasCorrectAnswer = options.some(option => option.is_correct);
        if (!hasCorrectAnswer) {
            return res.status(400).json({
                success: false,
                message: '必须设置至少一个正确答案'
            });
        }

        // 插入题目
        const [questionResult] = await connection.execute(
            `INSERT INTO questions
             (bank_id, question_text, question_image, question_type, explanation_text, explanation_image)
             VALUES (?, ?, ?, ?, ?, ?)`,
            [bank_id, question_text, question_image, question_type || 'single', explanation_text, explanation_image]
        );

        const questionId = questionResult.insertId;

        // 插入选项
        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            await connection.execute(
                'INSERT INTO question_options (question_id, option_label, option_text, option_image, is_correct) VALUES (?, ?, ?, ?, ?)',
                [questionId, option.option_label || String.fromCharCode(65 + i), option.option_text, option.option_image, option.is_correct || false]
            );
        }

        await connection.commit();

        res.json({
            success: true,
            message: '题目创建成功',
            data: { id: questionId }
        });
    } catch (error) {
        await connection.rollback();
        console.error('创建题目失败:', error);
        res.status(500).json({
            success: false,
            message: '创建题目失败'
        });
    } finally {
        connection.release();
    }
});

// 更新题目
router.put('/:id', async (req, res) => {
    const connection = await pool.getConnection();
    try {
        await connection.beginTransaction();

        const questionId = req.params.id;
        const {
            question_text,
            question_image,
            question_type,
            explanation_text,
            explanation_image,
            options
        } = req.body;

        // 更新题目
        const [questionResult] = await connection.execute(
            `UPDATE questions
             SET question_text = ?, question_image = ?, question_type = ?,
                 explanation_text = ?, explanation_image = ?
             WHERE id = ?`,
            [question_text, question_image, question_type, explanation_text, explanation_image, questionId]
        );

        if (questionResult.affectedRows === 0) {
            await connection.rollback();
            return res.status(404).json({
                success: false,
                message: '题目不存在'
            });
        }

        // 删除原有选项
        await connection.execute('DELETE FROM question_options WHERE question_id = ?', [questionId]);

        // 插入新选项
        if (options && Array.isArray(options)) {
            for (let i = 0; i < options.length; i++) {
                const option = options[i];
                await connection.execute(
                    'INSERT INTO question_options (question_id, option_label, option_text, option_image, is_correct) VALUES (?, ?, ?, ?, ?)',
                    [questionId, option.option_label || String.fromCharCode(65 + i), option.option_text, option.option_image, option.is_correct || false]
                );
            }
        }

        await connection.commit();

        res.json({
            success: true,
            message: '题目更新成功'
        });
    } catch (error) {
        await connection.rollback();
        console.error('更新题目失败:', error);
        res.status(500).json({
            success: false,
            message: '更新题目失败'
        });
    } finally {
        connection.release();
    }
});

// 删除题目
router.delete('/:id', async (req, res) => {
    try {
        const questionId = req.params.id;

        const [result] = await pool.execute('DELETE FROM questions WHERE id = ?', [questionId]);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '题目不存在'
            });
        }

        res.json({
            success: true,
            message: '题目删除成功'
        });
    } catch (error) {
        console.error('删除题目失败:', error);
        res.status(500).json({
            success: false,
            message: '删除题目失败'
        });
    }
});

module.exports = router;
