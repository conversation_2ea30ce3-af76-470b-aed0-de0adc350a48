const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// 获取答题题目（支持乱序）
router.get('/quiz/:bankId', async (req, res) => {
    try {
        const bankId = req.params.bankId;
        const shuffleQuestions = req.query.shuffle_questions === 'true';
        const shuffleOptions = req.query.shuffle_options === 'true';

        // 获取题库信息
        const [banks] = await pool.execute('SELECT * FROM question_banks WHERE id = ?', [bankId]);
        if (banks.length === 0) {
            return res.status(404).json({
                success: false,
                message: '题库不存在'
            });
        }

        // 获取题目列表
        let orderBy = 'q.id';
        if (shuffleQuestions) {
            orderBy = 'RAND()';
        }

        const [questions] = await pool.execute(`
            SELECT q.* FROM questions q
            WHERE q.bank_id = ?
            ORDER BY ${orderBy}
        `, [bankId]);

        if (questions.length === 0) {
            return res.status(404).json({
                success: false,
                message: '该题库暂无题目'
            });
        }

        // 获取每个题目的选项
        for (let question of questions) {
            // 根据答题模式决定是否返回正确答案信息
            const quizMode = req.query.quiz_mode || 'exam';
            let selectFields = 'id, option_label, option_text, option_image';
            if (quizMode === 'immediate') {
                selectFields += ', is_correct';
            }

            // 始终按标签顺序获取选项
            const [options] = await pool.execute(`
                SELECT ${selectFields}
                FROM question_options
                WHERE question_id = ?
                ORDER BY option_label
            `, [question.id]);

            // 如果需要乱序，只打乱选项内容，保持A、B、C、D标签顺序
            if (shuffleOptions && options.length > 0) {
                // 提取选项内容（除了标签）
                const optionContents = options.map(opt => ({
                    option_text: opt.option_text,
                    option_image: opt.option_image,
                    is_correct: opt.is_correct
                }));

                // 打乱内容数组
                for (let i = optionContents.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [optionContents[i], optionContents[j]] = [optionContents[j], optionContents[i]];
                }

                // 重新分配内容到固定的A、B、C、D标签
                options.forEach((opt, index) => {
                    if (optionContents[index]) {
                        opt.option_text = optionContents[index].option_text;
                        opt.option_image = optionContents[index].option_image;
                        opt.is_correct = optionContents[index].is_correct;
                    }
                });
            }

            question.options = options;
        }

        res.json({
            success: true,
            data: {
                bank: banks[0],
                questions,
                settings: {
                    shuffle_questions: shuffleQuestions,
                    shuffle_options: shuffleOptions
                }
            }
        });
    } catch (error) {
        console.error('获取答题题目失败:', error);
        res.status(500).json({
            success: false,
            message: '获取答题题目失败'
        });
    }
});

// 提交答案并计算成绩
router.post('/submit', async (req, res) => {
    try {
        const {
            bank_id,
            answers,
            start_time,
            end_time,
            shuffle_questions,
            shuffle_options
        } = req.body;

        if (!bank_id || !answers || !Array.isArray(answers)) {
            return res.status(400).json({
                success: false,
                message: '提交数据格式错误'
            });
        }

        // 获取题库信息
        const [banks] = await pool.execute('SELECT * FROM question_banks WHERE id = ?', [bank_id]);
        if (banks.length === 0) {
            return res.status(404).json({
                success: false,
                message: '题库不存在'
            });
        }

        let correctCount = 0;
        const results = [];

        // 逐题检查答案
        for (let answer of answers) {
            const { question_id, selected_options } = answer;

            // 获取正确答案
            const [correctOptions] = await pool.execute(
                'SELECT option_label FROM question_options WHERE question_id = ? AND is_correct = true',
                [question_id]
            );

            const correctLabels = correctOptions.map(opt => opt.option_label).sort();
            const selectedLabels = (selected_options || []).sort();

            // 判断答案是否正确（数组完全匹配）
            const isCorrect = JSON.stringify(correctLabels) === JSON.stringify(selectedLabels);

            if (isCorrect) {
                correctCount++;
            }

            results.push({
                question_id,
                selected_options: selectedLabels,
                correct_options: correctLabels,
                is_correct: isCorrect
            });
        }

        const totalQuestions = answers.length;
        const score = totalQuestions > 0 ? (correctCount / totalQuestions * 100) : 0;
        const duration = Math.floor((new Date(end_time) - new Date(start_time)) / 1000);

        // 保存答题记录
        const [recordResult] = await pool.execute(`
            INSERT INTO answer_records
            (bank_id, total_questions, correct_answers, score, start_time, end_time, duration, shuffle_questions, shuffle_options)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            bank_id,
            totalQuestions,
            correctCount,
            score,
            new Date(start_time),
            new Date(end_time),
            duration,
            shuffle_questions || false,
            shuffle_options || false
        ]);

        res.json({
            success: true,
            data: {
                record_id: recordResult.insertId,
                bank_name: banks[0].name,
                total_questions: totalQuestions,
                correct_answers: correctCount,
                score: Math.round(score * 100) / 100,
                duration,
                results
            }
        });
    } catch (error) {
        console.error('提交答案失败:', error);
        res.status(500).json({
            success: false,
            message: '提交答案失败'
        });
    }
});

// 获取答题记录列表
router.get('/records', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const bankId = req.query.bank_id;
        const offset = (page - 1) * limit;

        let whereClause = '';
        let params = [];

        if (bankId) {
            whereClause = 'WHERE ar.bank_id = ?';
            params.push(bankId);
        }

        // 获取总数
        const [countResult] = await pool.execute(
            `SELECT COUNT(*) as total FROM answer_records ar ${whereClause}`,
            params
        );
        const total = countResult[0].total;

        // 获取记录列表
        const [records] = await pool.execute(`
            SELECT
                ar.*,
                qb.name as bank_name
            FROM answer_records ar
            JOIN question_banks qb ON ar.bank_id = qb.id
            ${whereClause}
            ORDER BY ar.created_at DESC
        `, params);

        res.json({
            success: true,
            data: {
                records,
                pagination: {
                    current_page: page,
                    total_pages: Math.ceil(total / limit),
                    total_items: total,
                    items_per_page: limit
                }
            }
        });
    } catch (error) {
        console.error('获取答题记录失败:', error);
        res.status(500).json({
            success: false,
            message: '获取答题记录失败'
        });
    }
});

// 获取单个答题记录详情
router.get('/records/:id', async (req, res) => {
    try {
        const recordId = req.params.id;

        const [records] = await pool.execute(`
            SELECT 
                ar.*,
                qb.name as bank_name
            FROM answer_records ar
            JOIN question_banks qb ON ar.bank_id = qb.id
            WHERE ar.id = ?
        `, [recordId]);

        if (records.length === 0) {
            return res.status(404).json({
                success: false,
                message: '答题记录不存在'
            });
        }

        res.json({
            success: true,
            data: records[0]
        });
    } catch (error) {
        console.error('获取答题记录详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取答题记录详情失败'
        });
    }
});

module.exports = router;
