<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库系统 - 首页</title>
    <link rel="stylesheet" href="/css/common.css">
    <style>
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .bank-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .bank-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .bank-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .bank-card h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .bank-card p {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .bank-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #888;
        }

        .quiz-options {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .quiz-options label {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }

        .quiz-options input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .bank-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .bank-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <a href="/" class="logo">📚 题库系统</a>
            <ul class="nav-links">
                <li><a href="/">首页</a></li>
                <li><a href="/admin">管理后台</a></li>
            </ul>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero">
        <div class="container">
            <h1>📚 在线题库系统</h1>
            <p>选择题库开始答题，提升你的知识水平</p>
        </div>
    </section>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 题库列表 -->
        <div id="bankList" class="bank-grid">
            <div class="loading">
                <div class="spinner"></div>
            </div>
        </div>

        <!-- 分页 -->
        <div id="pagination"></div>
    </main>

    <script src="/js/common.js"></script>
    <script>
        let currentPage = 1;
        const pageSize = 12;

        // 加载题库列表
        async function loadBanks(page = 1) {
            try {
                showLoading(document.getElementById('bankList'));

                const response = await apiGet(`/banks?page=${page}&limit=${pageSize}`);

                if (response.success) {
                    renderBanks(response.data.banks);
                    renderPagination(response.data.pagination);
                } else {
                    showMessage('加载题库列表失败', 'error');
                }
            } catch (error) {
                console.error('加载题库列表失败:', error);
                showMessage('加载题库列表失败: ' + error.message, 'error');
                document.getElementById('bankList').innerHTML = '<div class="empty-state"><h3>加载失败</h3><p>请刷新页面重试</p></div>';
            }
        }

        // 渲染题库列表
        function renderBanks(banks) {
            const bankList = document.getElementById('bankList');

            if (banks.length === 0) {
                bankList.innerHTML = `
                    <div class="empty-state">
                        <h3>暂无题库</h3>
                        <p>请联系管理员添加题库</p>
                        <a href="/admin" class="btn btn-primary">前往管理后台</a>
                    </div>
                `;
                return;
            }

            bankList.innerHTML = banks.map(bank => `
                <div class="bank-card">
                    <h3>${bank.name}</h3>
                    <p>${bank.description || '暂无描述'}</p>
                    <div class="bank-meta">
                        <span>📝 ${bank.question_count} 道题目</span>
                        <span>📅 ${formatDate(bank.created_at)}</span>
                    </div>
                    
                    <div class="quiz-options">
                        <label>
                            <input type="checkbox" id="shuffle_questions_${bank.id}">
                            题目乱序
                        </label>
                        <label>
                            <input type="checkbox" id="shuffle_options_${bank.id}">
                            选项乱序
                        </label>
                        <label>
                            <input type="radio" name="quiz_mode_${bank.id}" value="immediate" id="immediate_mode_${bank.id}" checked>
                            即时反馈模式（答一题显示一题答案）
                        </label>
                        <label>
                            <input type="radio" name="quiz_mode_${bank.id}" value="exam" id="exam_mode_${bank.id}">
                            考试模式（答完全部题再显示答案）
                        </label>
                    </div>
                    
                    <button class="btn btn-primary btn-lg" onclick="startQuiz(${bank.id})" 
                            ${bank.question_count === 0 ? 'disabled' : ''}>
                        ${bank.question_count === 0 ? '暂无题目' : '开始答题'}
                    </button>
                </div>
            `).join('');
        }

        // 渲染分页
        function renderPagination(pagination) {
            const paginationElement = document.getElementById('pagination');
            paginationElement.innerHTML = createPagination(pagination, (page) => {
                currentPage = page;
                loadBanks(page);
            });
        }

        // 开始答题
        function startQuiz(bankId) {
            const shuffleQuestions = document.getElementById(`shuffle_questions_${bankId}`).checked;
            const shuffleOptions = document.getElementById(`shuffle_options_${bankId}`).checked;
            const quizMode = document.querySelector(`input[name="quiz_mode_${bankId}"]:checked`).value;

            const params = new URLSearchParams();
            if (shuffleQuestions) params.append('shuffle_questions', 'true');
            if (shuffleOptions) params.append('shuffle_options', 'true');
            params.append('quiz_mode', quizMode);

            const url = `/quiz/${bankId}?${params.toString()}`;
            window.location.href = url;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadBanks();
        });
    </script>
</body>

</html>